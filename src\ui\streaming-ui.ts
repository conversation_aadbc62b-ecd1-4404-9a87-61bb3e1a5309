import { StreamingEvent, UIConfig, ExecutionPlan, TaskResult } from '@/types';
import { logger } from '@/utils/logger';
import chalk from 'chalk';
import ora, { Ora } from 'ora';
import { EventEmitter } from 'events';

export class StreamingUI extends EventEmitter {
  private config: UIConfig;
  private spinners: Map<string, Ora> = new Map();
  private currentPlan: ExecutionPlan | null = null;
  private startTime: Date | null = null;

  constructor(config: UIConfig = {
    theme: 'auto',
    verbose: false,
    showProgress: true,
    showTimestamps: true,
    maxOutputLines: 50,
  }) {
    super();
    this.config = config;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.on('task_start', this.handleTaskStart.bind(this));
    this.on('task_progress', this.handleTaskProgress.bind(this));
    this.on('task_complete', this.handleTaskComplete.bind(this));
    this.on('task_error', this.handleTaskError.bind(this));
    this.on('plan_update', this.handlePlanUpdate.bind(this));
    this.on('output', this.handleOutput.bind(this));
  }

  public emitEvent(event: StreamingEvent): void {
    this.emit(event.type, event);
  }

  public startExecution(plan: ExecutionPlan): void {
    this.currentPlan = plan;
    this.startTime = new Date();
    
    this.displayHeader();
    this.displayPlanSummary(plan);
    
    this.emitEvent({
      type: 'plan_update',
      data: { plan, status: 'started' },
      timestamp: new Date(),
    });
  }

  public completeExecution(results: TaskResult[]): void {
    this.clearAllSpinners();
    this.displayExecutionSummary(results);
    
    this.emitEvent({
      type: 'plan_update',
      data: { results, status: 'completed' },
      timestamp: new Date(),
    });
  }

  private displayHeader(): void {
    const title = chalk.bold.blue('🤖 AI CLI Agent - Autonomous Execution');
    const separator = chalk.gray('─'.repeat(60));
    
    console.log('\n' + title);
    console.log(separator);
    
    if (this.config.showTimestamps && this.startTime) {
      console.log(chalk.gray(`Started: ${this.startTime.toLocaleTimeString()}`));
    }
  }

  private displayPlanSummary(plan: ExecutionPlan): void {
    console.log(chalk.bold('\n📋 Execution Plan:'));
    console.log(chalk.gray(`  Tasks: ${plan.tasks.length}`));
    console.log(chalk.gray(`  Parallel Groups: ${plan.parallelGroups.length}`));
    console.log(chalk.gray(`  Estimated Duration: ${this.formatDuration(plan.estimatedDuration)}`));
    
    if (this.config.verbose) {
      console.log(chalk.bold('\n📝 Task Details:'));
      plan.tasks.forEach((task, index) => {
        const priority = this.getPriorityIcon(task.priority);
        console.log(chalk.gray(`  ${index + 1}. ${priority} ${task.tool} - ${task.description}`));
      });
    }
    
    console.log('');
  }

  private handleTaskStart(event: StreamingEvent): void {
    const { taskId, taskName, tool } = event.data;
    
    if (this.config.showProgress) {
      const spinner = ora({
        text: chalk.blue(`${tool}: ${taskName}`),
        color: 'blue',
      }).start();
      
      this.spinners.set(taskId, spinner);
    }
    
    if (this.config.verbose) {
      const timestamp = this.config.showTimestamps ? chalk.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
      console.log(`${timestamp}${chalk.blue('▶')} Starting: ${chalk.bold(tool)}`);
    }
  }

  private handleTaskProgress(event: StreamingEvent): void {
    const { taskId, progress, message } = event.data;
    
    const spinner = this.spinners.get(taskId);
    if (spinner && this.config.showProgress) {
      spinner.text = chalk.blue(`${message} ${this.getProgressBar(progress)}`);
    }
    
    if (this.config.verbose) {
      const timestamp = this.config.showTimestamps ? chalk.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
      console.log(`${timestamp}${chalk.yellow('⚡')} ${message}`);
    }
  }

  private handleTaskComplete(event: StreamingEvent): void {
    const { taskId, taskName, tool, duration, output } = event.data;
    
    const spinner = this.spinners.get(taskId);
    if (spinner) {
      spinner.succeed(chalk.green(`${tool}: ${taskName} ${chalk.gray(`(${this.formatDuration(duration)})`)}`));
      this.spinners.delete(taskId);
    }
    
    if (this.config.verbose && output) {
      this.displayTaskOutput(output);
    }
  }

  private handleTaskError(event: StreamingEvent): void {
    const { taskId, taskName, tool, error, duration } = event.data;
    
    const spinner = this.spinners.get(taskId);
    if (spinner) {
      spinner.fail(chalk.red(`${tool}: ${taskName} ${chalk.gray(`(${this.formatDuration(duration)})`)}`));
      this.spinners.delete(taskId);
    }
    
    console.log(chalk.red(`  ❌ Error: ${error.message}`));
    
    if (this.config.verbose && error.stack) {
      console.log(chalk.gray(`  Stack: ${error.stack.split('\n')[1]?.trim()}`));
    }
  }

  private handlePlanUpdate(event: StreamingEvent): void {
    const { status, progress } = event.data;
    
    if (status === 'progress' && progress) {
      this.displayOverallProgress(progress);
    }
  }

  private handleOutput(event: StreamingEvent): void {
    const { content, type } = event.data;
    
    if (type === 'stream') {
      process.stdout.write(content);
    } else {
      console.log(content);
    }
  }

  private displayTaskOutput(output: any): void {
    if (typeof output === 'string') {
      const lines = output.split('\n').slice(0, this.config.maxOutputLines);
      lines.forEach(line => {
        console.log(chalk.gray(`    ${line}`));
      });
      
      if (output.split('\n').length > this.config.maxOutputLines) {
        console.log(chalk.gray(`    ... (${output.split('\n').length - this.config.maxOutputLines} more lines)`));
      }
    } else if (typeof output === 'object') {
      console.log(chalk.gray(`    ${JSON.stringify(output, null, 2)}`));
    }
  }

  private displayOverallProgress(progress: { completed: number; total: number; percentage: number }): void {
    const progressBar = this.getProgressBar(progress.percentage);
    const status = `${progress.completed}/${progress.total} tasks completed`;
    
    console.log(`\n${chalk.bold('Progress:')} ${progressBar} ${chalk.gray(status)}\n`);
  }

  private displayExecutionSummary(results: TaskResult[]): void {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log(chalk.bold('\n📊 Execution Summary:'));
    console.log(chalk.green(`  ✅ Successful: ${successful}`));
    
    if (failed > 0) {
      console.log(chalk.red(`  ❌ Failed: ${failed}`));
    }
    
    console.log(chalk.gray(`  ⏱️  Total Duration: ${this.formatDuration(totalDuration)}`));
    
    if (this.startTime) {
      const endTime = new Date();
      const wallClockTime = endTime.getTime() - this.startTime.getTime();
      console.log(chalk.gray(`  🕐 Wall Clock Time: ${this.formatDuration(wallClockTime)}`));
    }
    
    // Display failed tasks
    if (failed > 0) {
      console.log(chalk.bold('\n❌ Failed Tasks:'));
      results.filter(r => !r.success).forEach(result => {
        console.log(chalk.red(`  • ${result.taskId}: ${result.error?.message || 'Unknown error'}`));
      });
    }
    
    console.log('');
  }

  private clearAllSpinners(): void {
    this.spinners.forEach(spinner => {
      spinner.stop();
    });
    this.spinners.clear();
  }

  private getProgressBar(percentage: number): string {
    const width = 20;
    const filled = Math.round((percentage / 100) * width);
    const empty = width - filled;
    
    return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty)) + chalk.gray(` ${percentage.toFixed(1)}%`);
  }

  private getPriorityIcon(priority: number): string {
    if (priority >= 8) return chalk.red('🔴');
    if (priority >= 6) return chalk.yellow('🟡');
    if (priority >= 4) return chalk.blue('🔵');
    return chalk.gray('⚪');
  }

  private formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  public setConfig(config: Partial<UIConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public getConfig(): UIConfig {
    return { ...this.config };
  }

  public streamLLMResponse(onChunk: (chunk: string) => void): (chunk: string) => void {
    // Set up streaming for LLM responses
    const streamChunk = (chunk: string) => {
      if (this.config.verbose) {
        process.stdout.write(chalk.cyan(chunk));
      }
      onChunk(chunk);
    };

    return streamChunk;
  }

  public displayThinking(message: string): Ora {
    return ora({
      text: chalk.magenta(`🤔 ${message}`),
      color: 'magenta',
    }).start();
  }

  public displaySuccess(message: string): void {
    console.log(chalk.green(`✅ ${message}`));
  }

  public displayError(message: string): void {
    console.log(chalk.red(`❌ ${message}`));
  }

  public displayWarning(message: string): void {
    console.log(chalk.yellow(`⚠️  ${message}`));
  }

  public displayInfo(message: string): void {
    console.log(chalk.blue(`ℹ️  ${message}`));
  }

  public prompt(message: string): void {
    console.log(chalk.cyan(`❓ ${message}`));
  }

  public clear(): void {
    console.clear();
  }

  public newLine(): void {
    console.log('');
  }
}
