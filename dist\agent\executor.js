import { logger } from '@/utils/logger';
import PQueue from 'p-queue';
export class TaskExecutor {
    toolRegistry;
    executionQueue;
    constructor(toolRegistry) {
        this.toolRegistry = toolRegistry;
        this.executionQueue = new PQueue({ concurrency: 1 }); // Default sequential execution
    }
    async executePlan(plan, context) {
        logger.info('Starting plan execution', {
            planId: plan.id,
            taskCount: plan.tasks.length,
            parallelGroups: plan.parallelGroups.length
        });
        const results = [];
        const completedTasks = new Set();
        const failedTasks = new Set();
        try {
            // Execute tasks in dependency order with parallel groups
            const executionLevels = this.groupTasksByLevel(plan);
            for (const level of executionLevels) {
                const levelResults = await this.executeLevel(level, plan.parallelGroups, context, completedTasks, failedTasks);
                results.push(...levelResults);
                // Update completed/failed task sets
                levelResults.forEach(result => {
                    if (result.success) {
                        completedTasks.add(result.taskId);
                    }
                    else {
                        failedTasks.add(result.taskId);
                    }
                });
                // Check if we should continue execution
                if (this.shouldStopExecution(levelResults, plan)) {
                    logger.warn('Stopping execution due to critical failures');
                    break;
                }
            }
            logger.info('Plan execution completed', {
                planId: plan.id,
                totalTasks: plan.tasks.length,
                completedTasks: completedTasks.size,
                failedTasks: failedTasks.size,
            });
        }
        catch (error) {
            logger.error('Plan execution failed', error);
            throw error;
        }
        return results;
    }
    groupTasksByLevel(plan) {
        const levels = [];
        const taskLevels = new Map();
        // Calculate dependency levels
        const calculateLevel = (task) => {
            if (taskLevels.has(task.id)) {
                return taskLevels.get(task.id);
            }
            if (task.dependencies.length === 0) {
                taskLevels.set(task.id, 0);
                return 0;
            }
            let maxDepLevel = -1;
            for (const depId of task.dependencies) {
                const depTask = plan.tasks.find(t => t.id === depId);
                if (depTask) {
                    maxDepLevel = Math.max(maxDepLevel, calculateLevel(depTask));
                }
            }
            const level = maxDepLevel + 1;
            taskLevels.set(task.id, level);
            return level;
        };
        // Group tasks by level
        for (const task of plan.tasks) {
            const level = calculateLevel(task);
            while (levels.length <= level) {
                levels.push([]);
            }
            levels[level].push(task);
        }
        return levels;
    }
    async executeLevel(tasks, parallelGroups, context, completedTasks, failedTasks) {
        const results = [];
        // Filter out tasks that depend on failed tasks
        const executableTasks = tasks.filter(task => {
            return task.dependencies.every(depId => completedTasks.has(depId));
        });
        if (executableTasks.length === 0) {
            return results;
        }
        // Find parallel groups for this level
        const levelParallelGroups = parallelGroups.filter(group => group.taskIds.some(taskId => executableTasks.some(task => task.id === taskId)));
        const parallelTaskIds = new Set();
        levelParallelGroups.forEach(group => {
            group.taskIds.forEach(id => parallelTaskIds.add(id));
        });
        // Execute parallel groups
        for (const group of levelParallelGroups) {
            const groupTasks = executableTasks.filter(task => group.taskIds.includes(task.id));
            const groupResults = await this.executeParallelGroup(groupTasks, group.maxConcurrency, context);
            results.push(...groupResults);
        }
        // Execute remaining sequential tasks
        const sequentialTasks = executableTasks.filter(task => !parallelTaskIds.has(task.id));
        for (const task of sequentialTasks) {
            const result = await this.executeTask(task, context);
            results.push(result);
        }
        return results;
    }
    async executeParallelGroup(tasks, maxConcurrency, context) {
        logger.debug('Executing parallel group', {
            taskCount: tasks.length,
            maxConcurrency
        });
        const queue = new PQueue({ concurrency: maxConcurrency });
        const promises = tasks.map(task => queue.add(() => this.executeTask(task, context)));
        return Promise.all(promises);
    }
    async executeTask(task, context) {
        const startTime = Date.now();
        logger.task(task.id, 'Starting task execution', {
            tool: task.tool,
            priority: task.priority,
            attempt: task.retryCount + 1
        });
        try {
            // Execute the task with timeout
            const result = await this.executeWithTimeout(task, context);
            const duration = Date.now() - startTime;
            logger.task(task.id, 'Task completed successfully', { duration });
            return {
                taskId: task.id,
                success: true,
                output: result.data,
                duration,
                timestamp: new Date(),
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.task(task.id, 'Task failed', { error: error instanceof Error ? error.message : String(error), duration });
            // Retry logic
            if (task.retryCount < task.maxRetries && this.shouldRetry(error, task)) {
                task.retryCount++;
                logger.task(task.id, 'Retrying task', { attempt: task.retryCount + 1 });
                // Add exponential backoff delay
                const delay = Math.min(1000 * Math.pow(2, task.retryCount - 1), 10000);
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.executeTask(task, context);
            }
            return {
                taskId: task.id,
                success: false,
                output: null,
                error: error instanceof Error ? error : new Error(String(error)),
                duration,
                timestamp: new Date(),
            };
        }
    }
    async executeWithTimeout(task, context) {
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Task timeout after ${task.timeout}ms`));
            }, task.timeout);
            try {
                const result = await this.toolRegistry.executeTool(task.tool, task.parameters, context);
                clearTimeout(timeoutId);
                if (result.success) {
                    resolve(result);
                }
                else {
                    reject(new Error(result.error || 'Tool execution failed'));
                }
            }
            catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }
    shouldRetry(error, task) {
        // Don't retry critical operations
        const tool = this.toolRegistry.getTool(task.tool);
        if (tool?.riskLevel === 'critical') {
            return false;
        }
        // Don't retry certain error types
        const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();
        const nonRetryableErrors = [
            'permission denied',
            'access denied',
            'file not found',
            'directory not found',
            'invalid argument',
            'syntax error',
            'command not found',
        ];
        return !nonRetryableErrors.some(pattern => errorMessage.includes(pattern));
    }
    shouldStopExecution(levelResults, plan) {
        const failedResults = levelResults.filter(r => !r.success);
        if (failedResults.length === 0) {
            return false;
        }
        // Stop if critical tasks failed
        const criticalFailures = failedResults.filter(result => {
            const task = plan.tasks.find(t => t.id === result.taskId);
            const tool = task ? this.toolRegistry.getTool(task.tool) : null;
            return tool?.riskLevel === 'critical';
        });
        if (criticalFailures.length > 0) {
            return true;
        }
        // Stop if too many tasks failed in this level
        const failureRate = failedResults.length / levelResults.length;
        if (failureRate > 0.5 && levelResults.length > 2) {
            return true;
        }
        return false;
    }
    async executeTasksParallel(tasks, context, maxConcurrency = 3) {
        logger.debug('Executing tasks in parallel', {
            taskCount: tasks.length,
            maxConcurrency
        });
        const queue = new PQueue({ concurrency: maxConcurrency });
        const promises = tasks.map(task => queue.add(() => this.executeTask(task, context)));
        return Promise.all(promises);
    }
    async executeTasksSequential(tasks, context) {
        logger.debug('Executing tasks sequentially', { taskCount: tasks.length });
        const results = [];
        for (const task of tasks) {
            const result = await this.executeTask(task, context);
            results.push(result);
            // Stop on critical failure
            if (!result.success) {
                const tool = this.toolRegistry.getTool(task.tool);
                if (tool?.riskLevel === 'critical') {
                    logger.warn('Stopping sequential execution due to critical failure');
                    break;
                }
            }
        }
        return results;
    }
    getExecutionStats() {
        return {
            queueSize: this.executionQueue.size,
            pending: this.executionQueue.pending,
            isPaused: this.executionQueue.isPaused,
        };
    }
    pauseExecution() {
        this.executionQueue.pause();
        logger.info('Task execution paused');
    }
    resumeExecution() {
        this.executionQueue.start();
        logger.info('Task execution resumed');
    }
    clearQueue() {
        this.executionQueue.clear();
        logger.info('Task execution queue cleared');
    }
}
//# sourceMappingURL=executor.js.map