"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskSchema = exports.AgentConfigSchema = void 0;
const zod_1 = require("zod");
// Validation Schemas
exports.AgentConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['deepseek', 'openai', 'ollama', 'anthropic', 'gemini', 'mistral']),
    model: zod_1.z.string(),
    temperature: zod_1.z.number().min(0).max(2).optional(),
    maxTokens: zod_1.z.number().positive().optional(),
    timeout: zod_1.z.number().positive().optional(),
});
exports.TaskSchema = zod_1.z.object({
    id: zod_1.z.string(),
    type: zod_1.z.enum(['file_operation', 'shell_command', 'analysis', 'generation', 'network']),
    description: zod_1.z.string(),
    tool: zod_1.z.string(),
    parameters: zod_1.z.record(zod_1.z.any()),
    priority: zod_1.z.number().min(1).max(10),
    retryCount: zod_1.z.number().min(0).default(0),
    maxRetries: zod_1.z.number().min(0).default(3),
    timeout: zod_1.z.number().positive().default(30000),
    dependencies: zod_1.z.array(zod_1.z.string()).default([]),
});
//# sourceMappingURL=index.js.map