import { z } from 'zod';
// Validation Schemas
export const AgentConfigSchema = z.object({
    provider: z.enum(['deepseek', 'openai', 'ollama', 'anthropic', 'gemini', 'mistral']),
    model: z.string(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().positive().optional(),
    timeout: z.number().positive().optional(),
});
export const TaskSchema = z.object({
    id: z.string(),
    type: z.enum(['file_operation', 'shell_command', 'analysis', 'generation', 'network']),
    description: z.string(),
    tool: z.string(),
    parameters: z.record(z.any()),
    priority: z.number().min(1).max(10),
    retryCount: z.number().min(0).default(0),
    maxRetries: z.number().min(0).default(3),
    timeout: z.number().positive().default(30000),
    dependencies: z.array(z.string()).default([]),
});
//# sourceMappingURL=index.js.map