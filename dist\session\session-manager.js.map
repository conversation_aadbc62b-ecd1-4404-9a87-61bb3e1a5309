{"version": 3, "file": "session-manager.js", "sourceRoot": "", "sources": ["../../src/session/session-manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,+DAA2D;AAC3D,2CAAwC;AACxC,mCAAgC;AAChC,2CAA6B;AAC7B,gDAAwB;AACxB,wDAA0B;AAE1B,MAAa,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,MAAM,CAAM;IACZ,cAAc,GAAmB,IAAI,CAAC;IACtC,cAAc,CAAiB;IAC/B,WAAW,CAAS;IAE5B;QACE,IAAI,CAAC,MAAM,GAAG,IAAK,IAAY,CAAC;YAC9B,WAAW,EAAE,cAAc;YAC3B,UAAU,EAAE,UAAU;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,gCAAc,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACjE,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAEO,uBAAuB;QAC7B,kBAAE,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,IAAY,EACZ,WAAwB,EACxB,mBAA2B,OAAO,CAAC,GAAG,EAAE,EACxC,aAAsB,IAAI;QAE1B,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAE3E,qCAAqC;QACrC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;QAEzF,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,SAAS;YACb,IAAI;YACJ,OAAO,EAAE,GAAG;YACZ,YAAY,EAAE,GAAG;YACjB,OAAO;YACP,MAAM,EAAE,WAAW;YACnB,UAAU;SACX,CAAC;QAEF,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEjC,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAE9C,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEnD,0BAA0B;QAC1B,WAAW,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpD,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC9D,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YAC7E,GAAG,KAAK;YACR,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;YACpC,MAAM,EAAE;gBACN,GAAG,KAAK,CAAC,MAAM;gBACf,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;aAC5C;SACF,CAAC,CAAC,CAAC;QAEJ,4BAA4B;QAC5B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YAC/F,GAAG,KAAK;YACR,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;SACrC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YAC7F,GAAG,KAAK;YACR,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;SACrC,CAAC,CAAC,CAAC;QACJ,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE,CAAC,CAAC;YAC/F,GAAG,OAAO;YACV,QAAQ,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;SACrC,CAAC,CAAC,CAAC;QAEJ,MAAM,OAAO,GAAY,WAAW,CAAC;QACrC,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,6CAA6C;QAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAE9B,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAgB;QACvC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;QAEtE,4BAA4B;QAC5B,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,MAAM,kBAAE,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,SAAiB;QAC1C,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAE/C,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;QAErE,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,MAAM,kBAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxC,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAChD,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,eAAe;QAOpB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAU,CAAC;QAC1D,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,GAAG,CAAC;YACJ,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5B,YAAY,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;SACvC,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,YAAY;QAQvB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CACxC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjC,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;YACtE,IAAI,IAAI,GAAG,CAAC,CAAC;YAEb,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACpB,CAAC;YAAC,MAAM,CAAC;gBACP,+BAA+B;YACjC,CAAC;YAED,OAAO,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,CAAC;QAC9B,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9F,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,OAA8B;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QAED,iBAAiB;QACjB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC;QAE7C,qBAAqB;QACrB,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,YAAoB;QACtD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB;YAClD,EAAE,EAAE,YAAY;SACjB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEhD,IAAI,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,EAAE,YAAY,EAAE,CAAC,CAAC;QAEpE,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE;QACjD,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QAEhE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEvE,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,YAAY,GAAG,UAAU,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACrC,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAC3D,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,UAAkB;QAC9D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,kBAAE,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAkB;QAC3C,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAElD,qCAAqC;QACrC,WAAW,CAAC,EAAE,GAAG,IAAA,eAAM,GAAE,CAAC;QAC1B,WAAW,CAAC,IAAI,GAAG,GAAG,WAAW,CAAC,IAAI,aAAa,CAAC;QAEpD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CACtC,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,OAAO,CAAC,gBAAgB,EACpC,IAAI,CACL,CAAC;QAEF,uBAAuB;QACvB,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACtC,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;QAEvC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEhC,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACvE,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,OAAgB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC;QAEnE,MAAM,cAAc,GAAG;YACrB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,gBAAgB,EAAE,OAAO,CAAC,OAAO,CAAC,gBAAgB;SACnD,CAAC;QAEF,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACvB,QAAQ,CAAC,aAAa,CAAC,GAAG,cAAc,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC3C,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;CACF;AAjUD,wCAiUC"}