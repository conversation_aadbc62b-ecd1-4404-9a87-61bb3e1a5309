import { LLMMessage, LLMResponse, ToolCall } from '@/types';
export declare abstract class LL<PERSON>rovider {
    protected apiKey: string;
    protected model: string;
    protected temperature: number;
    protected maxTokens: number;
    protected timeout: number;
    constructor(config: {
        apiKey: string;
        model: string;
        temperature?: number;
        maxTokens?: number;
        timeout?: number;
    });
    abstract generateResponse(messages: LLMMessage[], tools?: any[], stream?: boolean): Promise<LLMResponse>;
    abstract generateStream(messages: LLMMessage[], tools?: any[], onChunk?: (chunk: string) => void): AsyncGenerator<string, LLMResponse>;
    abstract validateConnection(): Promise<boolean>;
    protected formatMessages(messages: LLMMessage[]): any[];
    protected formatTools(tools: any[]): any[];
    protected parseToolCalls(toolCalls: any[]): ToolCall[];
    protected handleError(error: any): never;
    getModel(): string;
    getProvider(): string;
}
export interface LLMProviderConfig {
    apiKey: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    baseUrl?: string;
}
//# sourceMappingURL=llm-provider.d.ts.map