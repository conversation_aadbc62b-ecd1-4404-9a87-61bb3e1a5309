"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = void 0;
const context_manager_1 = require("@/context/context-manager");
const logger_1 = require("@/utils/logger");
const nanoid_1 = require("nanoid");
const Conf = __importStar(require("conf"));
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
class SessionManager {
    static instance;
    config;
    currentSession = null;
    contextManager;
    sessionsDir;
    constructor() {
        this.config = new Conf({
            projectName: 'ai-cli-agent',
            configName: 'sessions',
        });
        this.contextManager = context_manager_1.ContextManager.getInstance();
        this.sessionsDir = path_1.default.join(this.config.path, '..', 'sessions');
        this.ensureSessionsDirectory();
    }
    static getInstance() {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager();
        }
        return SessionManager.instance;
    }
    ensureSessionsDirectory() {
        fs_extra_1.default.ensureDirSync(this.sessionsDir);
    }
    async createSession(name, agentConfig, workingDirectory = process.cwd(), persistent = true) {
        const sessionId = (0, nanoid_1.nanoid)();
        const now = new Date();
        logger_1.logger.info('Creating new session', { sessionId, name, workingDirectory });
        // Initialize context for the session
        const context = await this.contextManager.initializeContext(sessionId, workingDirectory);
        const session = {
            id: sessionId,
            name,
            created: now,
            lastAccessed: now,
            context,
            config: agentConfig,
            persistent,
        };
        if (persistent) {
            await this.saveSession(session);
        }
        this.currentSession = session;
        this.updateSessionsList(session);
        logger_1.logger.info('Session created', { sessionId, name });
        return session;
    }
    async loadSession(sessionId) {
        logger_1.logger.info('Loading session', { sessionId });
        const sessionPath = path_1.default.join(this.sessionsDir, `${sessionId}.json`);
        if (!await fs_extra_1.default.pathExists(sessionPath)) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        const sessionData = await fs_extra_1.default.readJson(sessionPath);
        // Restore dates from JSON
        sessionData.created = new Date(sessionData.created);
        sessionData.lastAccessed = new Date(sessionData.lastAccessed);
        sessionData.context.history = sessionData.context.history.map((entry) => ({
            ...entry,
            timestamp: new Date(entry.timestamp),
            result: {
                ...entry.result,
                timestamp: new Date(entry.result.timestamp),
            },
        }));
        // Restore memory timestamps
        sessionData.context.memory.shortTerm = sessionData.context.memory.shortTerm.map((entry) => ({
            ...entry,
            timestamp: new Date(entry.timestamp),
        }));
        sessionData.context.memory.longTerm = sessionData.context.memory.longTerm.map((entry) => ({
            ...entry,
            timestamp: new Date(entry.timestamp),
        }));
        sessionData.context.memory.patterns = sessionData.context.memory.patterns.map((pattern) => ({
            ...pattern,
            lastUsed: new Date(pattern.lastUsed),
        }));
        const session = sessionData;
        session.lastAccessed = new Date();
        // Update context manager with loaded context
        await this.contextManager.updateContext(session.context);
        this.currentSession = session;
        if (session.persistent) {
            await this.saveSession(session);
            this.updateSessionsList(session);
        }
        logger_1.logger.info('Session loaded', { sessionId, name: session.name });
        return session;
    }
    async saveSession(session) {
        if (!session.persistent) {
            return;
        }
        const sessionPath = path_1.default.join(this.sessionsDir, `${session.id}.json`);
        // Update last accessed time
        session.lastAccessed = new Date();
        await fs_extra_1.default.writeJson(sessionPath, session, { spaces: 2 });
        logger_1.logger.debug('Session saved', { sessionId: session.id });
    }
    async deleteSession(sessionId) {
        logger_1.logger.info('Deleting session', { sessionId });
        const sessionPath = path_1.default.join(this.sessionsDir, `${sessionId}.json`);
        if (await fs_extra_1.default.pathExists(sessionPath)) {
            await fs_extra_1.default.remove(sessionPath);
        }
        // Remove from sessions list
        const sessions = this.getSessionsList();
        const updatedSessions = sessions.filter(s => s.id !== sessionId);
        this.config.set('sessions', updatedSessions);
        if (this.currentSession?.id === sessionId) {
            this.currentSession = null;
        }
        logger_1.logger.info('Session deleted', { sessionId });
    }
    getCurrentSession() {
        return this.currentSession;
    }
    getSessionsList() {
        const sessions = this.config.get('sessions', []);
        return sessions.map(s => ({
            ...s,
            created: new Date(s.created),
            lastAccessed: new Date(s.lastAccessed),
        }));
    }
    async listSessions() {
        const sessionsList = this.getSessionsList();
        const sessionsWithSize = await Promise.all(sessionsList.map(async (session) => {
            const sessionPath = path_1.default.join(this.sessionsDir, `${session.id}.json`);
            let size = 0;
            try {
                const stats = await fs_extra_1.default.stat(sessionPath);
                size = stats.size;
            }
            catch {
                // Session file might not exist
            }
            return { ...session, size };
        }));
        return sessionsWithSize.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime());
    }
    async updateCurrentSession(updates) {
        if (!this.currentSession) {
            throw new Error('No active session');
        }
        // Update context
        const updatedContext = await this.contextManager.updateContext(updates);
        this.currentSession.context = updatedContext;
        // Save if persistent
        if (this.currentSession.persistent) {
            await this.saveSession(this.currentSession);
        }
    }
    async switchWorkingDirectory(newDirectory) {
        if (!this.currentSession) {
            throw new Error('No active session');
        }
        logger_1.logger.info('Switching working directory', {
            from: this.currentSession.context.workingDirectory,
            to: newDirectory
        });
        const resolvedPath = path_1.default.resolve(newDirectory);
        if (!await fs_extra_1.default.pathExists(resolvedPath)) {
            throw new Error(`Directory does not exist: ${resolvedPath}`);
        }
        await this.updateCurrentSession({ workingDirectory: resolvedPath });
        // Update sessions list
        this.updateSessionsList(this.currentSession);
    }
    async cleanupOldSessions(maxAge = 30) {
        logger_1.logger.info('Cleaning up old sessions', { maxAgeDays: maxAge });
        const sessions = await this.listSessions();
        const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
        let deletedCount = 0;
        for (const session of sessions) {
            if (session.lastAccessed < cutoffDate) {
                await this.deleteSession(session.id);
                deletedCount++;
            }
        }
        logger_1.logger.info('Session cleanup completed', { deletedCount });
        return deletedCount;
    }
    async exportSession(sessionId, exportPath) {
        const session = await this.loadSession(sessionId);
        await fs_extra_1.default.writeJson(exportPath, session, { spaces: 2 });
        logger_1.logger.info('Session exported', { sessionId, exportPath });
    }
    async importSession(importPath) {
        const sessionData = await fs_extra_1.default.readJson(importPath);
        // Generate new ID to avoid conflicts
        sessionData.id = (0, nanoid_1.nanoid)();
        sessionData.name = `${sessionData.name} (imported)`;
        const session = await this.createSession(sessionData.name, sessionData.config, sessionData.context.workingDirectory, true);
        // Restore context data
        session.context = sessionData.context;
        session.context.sessionId = session.id;
        await this.saveSession(session);
        logger_1.logger.info('Session imported', { sessionId: session.id, importPath });
        return session;
    }
    updateSessionsList(session) {
        const sessions = this.getSessionsList();
        const existingIndex = sessions.findIndex(s => s.id === session.id);
        const sessionSummary = {
            id: session.id,
            name: session.name,
            created: session.created,
            lastAccessed: session.lastAccessed,
            workingDirectory: session.context.workingDirectory,
        };
        if (existingIndex >= 0) {
            sessions[existingIndex] = sessionSummary;
        }
        else {
            sessions.push(sessionSummary);
        }
        this.config.set('sessions', sessions);
    }
    getSessionsDirectory() {
        return this.sessionsDir;
    }
    async getSessionSize(sessionId) {
        const sessionPath = path_1.default.join(this.sessionsDir, `${sessionId}.json`);
        try {
            const stats = await fs_extra_1.default.stat(sessionPath);
            return stats.size;
        }
        catch {
            return 0;
        }
    }
}
exports.SessionManager = SessionManager;
//# sourceMappingURL=session-manager.js.map