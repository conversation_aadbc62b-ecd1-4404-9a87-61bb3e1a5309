import { LL<PERSON>rovider, LLMProviderConfig } from './llm-provider';
import { LLMMessage, LLMResponse } from '@/types';
export declare class OpenAIProvider extends LLMProvider {
    private client;
    constructor(config: LLMProviderConfig);
    generateResponse(messages: LLMMessage[], tools?: any[], stream?: boolean): Promise<LLMResponse>;
    generateStream(messages: LLMMessage[], tools?: any[], onChunk?: (chunk: string) => void): AsyncGenerator<string, LLMResponse>;
    validateConnection(): Promise<boolean>;
}
//# sourceMappingURL=openai.d.ts.map