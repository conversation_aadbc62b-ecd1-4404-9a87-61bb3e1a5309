import { <PERSON><PERSON><PERSON><PERSON>, LLMProviderConfig } from './llm-provider';
import { LLMMessage, LLMResponse } from '@/types';
import Anthropic from '@anthropic-ai/sdk';

export class AnthropicProvider extends LLMProvider {
  private client: Anthropic;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.client = new Anthropic({
      apiKey: this.apiKey,
      baseURL: config.baseUrl,
      timeout: this.timeout,
    });
  }

  async generateResponse(
    messages: LLMMessage[],
    tools?: any[],
    stream: boolean = false
  ): Promise<LLMResponse> {
    try {
      const { system, messages: anthropicMessages } = this.formatAnthropicMessages(messages);
      
      const params: Anthropic.MessageCreateParams = {
        model: this.model,
        messages: anthropicMessages,
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        stream,
      };

      if (system) {
        params.system = system;
      }

      if (tools && tools.length > 0) {
        params.tools = this.formatAnthropicTools(tools);
      }

      const response = await this.client.messages.create(params) as any;

      let content = '';
      let toolCalls: any[] = [];

      if (Array.isArray(response.content)) {
        for (const block of response.content) {
          if (block.type === 'text') {
            content += block.text;
          } else if (block.type === 'tool_use') {
            toolCalls.push({
              id: block.id,
              type: 'function',
              function: {
                name: block.name,
                arguments: JSON.stringify(block.input),
              },
            });
          }
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
        usage: response.usage ? {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
        } : undefined,
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  async *generateStream(
    messages: LLMMessage[],
    tools?: any[],
    onChunk?: (chunk: string) => void
  ): AsyncGenerator<string, LLMResponse, unknown> {
    try {
      const { system, messages: anthropicMessages } = this.formatAnthropicMessages(messages);
      
      const params: Anthropic.MessageCreateParams = {
        model: this.model,
        messages: anthropicMessages,
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        stream: true,
      };

      if (system) {
        params.system = system;
      }

      if (tools && tools.length > 0) {
        params.tools = this.formatAnthropicTools(tools);
      }

      const stream = await this.client.messages.create(params);
      
      let fullContent = '';
      let toolCalls: any[] = [];
      let usage: any;

      for await (const event of stream) {
        if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
          const chunk = event.delta.text;
          fullContent += chunk;
          if (onChunk) onChunk(chunk);
          yield chunk;
        } else if (event.type === 'content_block_start' && event.content_block.type === 'tool_use') {
          toolCalls.push({
            id: event.content_block.id,
            type: 'function',
            function: {
              name: event.content_block.name,
              arguments: JSON.stringify(event.content_block.input),
            },
          });
        } else if (event.type === 'message_delta' && event.usage) {
          usage = event.usage;
        }
      }

      return {
        content: fullContent,
        toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
        usage: usage ? {
          promptTokens: usage.input_tokens || 0,
          completionTokens: usage.output_tokens || 0,
          totalTokens: (usage.input_tokens || 0) + (usage.output_tokens || 0),
        } : undefined,
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.client.messages.create({
        model: this.model,
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1,
      });
      return true;
    } catch {
      return false;
    }
  }

  private formatAnthropicMessages(messages: LLMMessage[]): {
    system?: string;
    messages: Anthropic.MessageParam[];
  } {
    let system: string | undefined;
    const anthropicMessages: Anthropic.MessageParam[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        system = msg.content;
      } else if (msg.role === 'tool') {
        // Handle tool response
        anthropicMessages.push({
          role: 'user',
          content: [
            {
              type: 'tool_result',
              tool_use_id: msg.toolCallId!,
              content: msg.content,
            },
          ],
        });
      } else {
        anthropicMessages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        });
      }
    }

    return { system, messages: anthropicMessages };
  }

  private formatAnthropicTools(tools: any[]): Anthropic.Tool[] {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: {
        type: 'object',
        properties: tool.parameters.reduce((acc: any, param: any) => {
          acc[param.name] = {
            type: param.type,
            description: param.description,
            ...(param.enum && { enum: param.enum }),
          };
          return acc;
        }, {}),
        required: tool.parameters.filter((p: any) => p.required).map((p: any) => p.name),
      },
    }));
  }
}
