import { Tool, ToolR<PERSON>ult, AgentContext } from '@/types';
import { execa } from 'execa';
import { logger } from '@/utils/logger';
import path from 'path';
import fs from 'fs-extra';

export class ShellCommandsTool {
  getExecuteCommandTool(): Tool {
    return {
      name: 'execute_command',
      description: 'Execute a shell command',
      category: 'shell',
      riskLevel: 'high',
      parallel: true,
      parameters: [
        {
          name: 'command',
          type: 'string',
          description: 'Command to execute',
          required: true,
        },
        {
          name: 'args',
          type: 'array',
          description: 'Command arguments',
          required: false,
          default: [],
        },
        {
          name: 'cwd',
          type: 'string',
          description: 'Working directory (default: current)',
          required: false,
        },
        {
          name: 'timeout',
          type: 'number',
          description: 'Timeout in milliseconds (default: 30000)',
          required: false,
          default: 30000,
        },
        {
          name: 'env',
          type: 'object',
          description: 'Environment variables',
          required: false,
          default: {},
        },
        {
          name: 'shell',
          type: 'boolean',
          description: 'Run in shell (default: true)',
          required: false,
          default: true,
        },
      ],
      execute: async (params, context) => {
        try {
          const workingDir = params.cwd 
            ? path.resolve(context.workingDirectory, params.cwd)
            : context.workingDirectory;

          logger.tool('execute_command', `Executing: ${params.command}`, {
            args: params.args,
            cwd: workingDir,
          });

          const result = await execa(params.command, params.args, {
            cwd: workingDir,
            timeout: params.timeout,
            env: { ...process.env, ...params.env },
            shell: params.shell,
            all: true,
          });

          return {
            success: true,
            data: {
              command: params.command,
              args: params.args,
              stdout: result.stdout,
              stderr: result.stderr,
              exitCode: result.exitCode,
              duration: (result as any).durationMs || 0,
              cwd: workingDir,
            },
          };
        } catch (error: any) {
          return {
            success: false,
            data: {
              command: params.command,
              args: params.args,
              stdout: error.stdout || '',
              stderr: error.stderr || '',
              exitCode: error.exitCode || -1,
              duration: error.durationMs || 0,
            },
            error: `Command failed: ${error.message}`,
          };
        }
      },
    };
  }

  getExecuteScriptTool(): Tool {
    return {
      name: 'execute_script',
      description: 'Execute a script file',
      category: 'shell',
      riskLevel: 'high',
      parallel: false,
      parameters: [
        {
          name: 'scriptPath',
          type: 'string',
          description: 'Path to the script file',
          required: true,
        },
        {
          name: 'interpreter',
          type: 'string',
          description: 'Script interpreter (e.g., "node", "python", "bash")',
          required: false,
        },
        {
          name: 'args',
          type: 'array',
          description: 'Script arguments',
          required: false,
          default: [],
        },
        {
          name: 'cwd',
          type: 'string',
          description: 'Working directory (default: script directory)',
          required: false,
        },
        {
          name: 'timeout',
          type: 'number',
          description: 'Timeout in milliseconds (default: 60000)',
          required: false,
          default: 60000,
        },
      ],
      execute: async (params, context) => {
        try {
          const scriptPath = path.resolve(context.workingDirectory, params.scriptPath);
          
          if (!await fs.pathExists(scriptPath)) {
            return {
              success: false,
              data: null,
              error: 'Script file does not exist',
            };
          }

          const workingDir = params.cwd 
            ? path.resolve(context.workingDirectory, params.cwd)
            : path.dirname(scriptPath);

          let command: string;
          let args: string[];

          if (params.interpreter) {
            command = params.interpreter;
            args = [scriptPath, ...params.args];
          } else {
            // Try to determine interpreter from file extension
            const ext = path.extname(scriptPath).toLowerCase();
            switch (ext) {
              case '.js':
              case '.mjs':
                command = 'node';
                args = [scriptPath, ...params.args];
                break;
              case '.py':
                command = 'python';
                args = [scriptPath, ...params.args];
                break;
              case '.sh':
              case '.bash':
                command = 'bash';
                args = [scriptPath, ...params.args];
                break;
              case '.ps1':
                command = 'powershell';
                args = ['-File', scriptPath, ...params.args];
                break;
              default:
                command = scriptPath;
                args = params.args;
            }
          }

          logger.tool('execute_script', `Executing script: ${scriptPath}`, {
            interpreter: command,
            args,
            cwd: workingDir,
          });

          const result = await execa(command, args, {
            cwd: workingDir,
            timeout: params.timeout,
            all: true,
          });

          return {
            success: true,
            data: {
              script: scriptPath,
              interpreter: command,
              args: params.args,
              stdout: result.stdout,
              stderr: result.stderr,
              exitCode: result.exitCode,
              duration: (result as any).durationMs || 0,
              cwd: workingDir,
            },
          };
        } catch (error: any) {
          return {
            success: false,
            data: {
              script: params.scriptPath,
              stdout: error.stdout || '',
              stderr: error.stderr || '',
              exitCode: error.exitCode || -1,
              duration: error.durationMs || 0,
            },
            error: `Script execution failed: ${error.message}`,
          };
        }
      },
    };
  }

  getProcessManagementTool(): Tool {
    return {
      name: 'process_management',
      description: 'Manage system processes',
      category: 'system',
      riskLevel: 'critical',
      parallel: false,
      parameters: [
        {
          name: 'action',
          type: 'string',
          description: 'Action to perform',
          required: true,
          enum: ['list', 'kill', 'info'],
        },
        {
          name: 'pid',
          type: 'number',
          description: 'Process ID (required for kill/info actions)',
          required: false,
        },
        {
          name: 'signal',
          type: 'string',
          description: 'Signal to send when killing (default: SIGTERM)',
          required: false,
          default: 'SIGTERM',
        },
        {
          name: 'pattern',
          type: 'string',
          description: 'Pattern to filter processes by name',
          required: false,
        },
      ],
      execute: async (params, context) => {
        try {
          switch (params.action) {
            case 'list':
              return await this.listProcesses(params.pattern);
            case 'kill':
              if (!params.pid) {
                return {
                  success: false,
                  data: null,
                  error: 'PID is required for kill action',
                };
              }
              return await this.killProcess(params.pid, params.signal);
            case 'info':
              if (!params.pid) {
                return {
                  success: false,
                  data: null,
                  error: 'PID is required for info action',
                };
              }
              return await this.getProcessInfo(params.pid);
            default:
              return {
                success: false,
                data: null,
                error: `Unknown action: ${params.action}`,
              };
          }
        } catch (error) {
          return {
            success: false,
            data: null,
            error: `Process management failed: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
    };
  }

  getEnvironmentTool(): Tool {
    return {
      name: 'environment',
      description: 'Get or set environment variables',
      category: 'system',
      riskLevel: 'medium',
      parallel: true,
      parameters: [
        {
          name: 'action',
          type: 'string',
          description: 'Action to perform',
          required: true,
          enum: ['get', 'set', 'list', 'unset'],
        },
        {
          name: 'name',
          type: 'string',
          description: 'Environment variable name',
          required: false,
        },
        {
          name: 'value',
          type: 'string',
          description: 'Environment variable value (for set action)',
          required: false,
        },
        {
          name: 'pattern',
          type: 'string',
          description: 'Pattern to filter environment variables',
          required: false,
        },
      ],
      execute: async (params, context) => {
        try {
          switch (params.action) {
            case 'get':
              if (!params.name) {
                return {
                  success: false,
                  data: null,
                  error: 'Variable name is required for get action',
                };
              }
              return {
                success: true,
                data: {
                  name: params.name,
                  value: process.env[params.name] || null,
                  exists: params.name in process.env,
                },
              };

            case 'set':
              if (!params.name || params.value === undefined) {
                return {
                  success: false,
                  data: null,
                  error: 'Variable name and value are required for set action',
                };
              }
              const oldValue = process.env[params.name];
              process.env[params.name] = params.value;
              return {
                success: true,
                data: {
                  name: params.name,
                  oldValue,
                  newValue: params.value,
                  set: true,
                },
              };

            case 'unset':
              if (!params.name) {
                return {
                  success: false,
                  data: null,
                  error: 'Variable name is required for unset action',
                };
              }
              const valueToUnset = process.env[params.name];
              delete process.env[params.name];
              return {
                success: true,
                data: {
                  name: params.name,
                  previousValue: valueToUnset,
                  unset: true,
                },
              };

            case 'list':
              let envVars = Object.entries(process.env);

              if (params.pattern) {
                const regex = new RegExp(params.pattern, 'i');
                envVars = envVars.filter(([name]) => regex.test(name));
              }

              return {
                success: true,
                data: {
                  variables: envVars.map(([name, value]) => ({ name, value })),
                  count: envVars.length,
                  pattern: params.pattern,
                },
              };

            default:
              return {
                success: false,
                data: null,
                error: `Unknown action: ${params.action}`,
              };
          }
        } catch (error) {
          return {
            success: false,
            data: null,
            error: `Environment operation failed: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
    };
  }

  private async listProcesses(pattern?: string): Promise<ToolResult> {
    try {
      const isWindows = process.platform === 'win32';
      const command = isWindows ? 'tasklist' : 'ps';
      const args = isWindows ? ['/fo', 'csv'] : ['aux'];

      const result = await execa(command, args, { all: true });
      const lines = result.stdout.split('\n').filter(line => line.trim());

      let processes: any[] = [];

      if (isWindows) {
        // Parse Windows tasklist CSV output
        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
        processes = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.replace(/"/g, '').trim());
          const proc: any = {};
          headers.forEach((header, index) => {
            proc[header.toLowerCase().replace(/\s+/g, '_')] = values[index] || '';
          });
          return proc;
        });
      } else {
        // Parse Unix ps output
        processes = lines.slice(1).map(line => {
          const parts = line.trim().split(/\s+/);
          return {
            user: parts[0],
            pid: parseInt(parts[1]),
            cpu: parseFloat(parts[2]),
            mem: parseFloat(parts[3]),
            vsz: parseInt(parts[4]),
            rss: parseInt(parts[5]),
            tty: parts[6],
            stat: parts[7],
            start: parts[8],
            time: parts[9],
            command: parts.slice(10).join(' '),
          };
        });
      }

      if (pattern) {
        const regex = new RegExp(pattern, 'i');
        processes = processes.filter(proc =>
          regex.test(proc.command || proc.image_name || '')
        );
      }

      return {
        success: true,
        data: {
          processes,
          count: processes.length,
          pattern,
        },
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: `Failed to list processes: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  private async killProcess(pid: number, signal: string = 'SIGTERM'): Promise<ToolResult> {
    try {
      const isWindows = process.platform === 'win32';

      if (isWindows) {
        await execa('taskkill', ['/PID', pid.toString(), '/F']);
      } else {
        await execa('kill', [`-${signal}`, pid.toString()]);
      }

      return {
        success: true,
        data: {
          pid,
          signal,
          killed: true,
        },
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: `Failed to kill process: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  private async getProcessInfo(pid: number): Promise<ToolResult> {
    try {
      const isWindows = process.platform === 'win32';

      if (isWindows) {
        const result = await execa('tasklist', ['/PID', pid.toString(), '/fo', 'csv', '/v']);
        const lines = result.stdout.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          return {
            success: false,
            data: null,
            error: 'Process not found',
          };
        }

        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
        const values = lines[1].split(',').map(v => v.replace(/"/g, '').trim());

        const processInfo: any = {};
        headers.forEach((header, index) => {
          processInfo[header.toLowerCase().replace(/\s+/g, '_')] = values[index] || '';
        });

        return {
          success: true,
          data: { pid, info: processInfo },
        };
      } else {
        const result = await execa('ps', ['-p', pid.toString(), '-o', 'pid,ppid,user,cpu,mem,vsz,rss,tty,stat,start,time,command']);
        const lines = result.stdout.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          return {
            success: false,
            data: null,
            error: 'Process not found',
          };
        }

        const parts = lines[1].trim().split(/\s+/);
        const processInfo = {
          pid: parseInt(parts[0]),
          ppid: parseInt(parts[1]),
          user: parts[2],
          cpu: parseFloat(parts[3]),
          mem: parseFloat(parts[4]),
          vsz: parseInt(parts[5]),
          rss: parseInt(parts[6]),
          tty: parts[7],
          stat: parts[8],
          start: parts[9],
          time: parts[10],
          command: parts.slice(11).join(' '),
        };

        return {
          success: true,
          data: { pid, info: processInfo },
        };
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        error: `Failed to get process info: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}
