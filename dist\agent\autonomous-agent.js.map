{"version": 3, "file": "autonomous-agent.js", "sourceRoot": "", "sources": ["../../src/agent/autonomous-agent.ts"], "names": [], "mappings": ";;;AAEA,2CAA8C;AAC9C,yDAAqD;AACrD,uCAAwC;AACxC,yCAA0C;AAC1C,+DAA2D;AAC3D,+DAA2D;AAC3D,2CAAwC;AACxC,mCAAgC;AAEhC,MAAa,eAAe;IAClB,QAAQ,CAAc;IACtB,YAAY,CAAe;IAC3B,OAAO,CAAc;IACrB,QAAQ,CAAe;IACvB,cAAc,CAAiB;IAC/B,cAAc,CAAiB;IAC/B,MAAM,CAAc;IACpB,mBAAmB,GAAiB,EAAE,CAAC;IAE/C,YAAY,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,2BAAe,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC9D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,EAAE;YACrE,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,qBAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,GAAG,gCAAc,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,cAAc,GAAG,gCAAc,CAAC,WAAW,EAAE,CAAC;QAEnD,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,sBAAsB;QAC5B,MAAM,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8IA8BqH,CAAC;QAE3I,IAAI,CAAC,mBAAmB,GAAG;YACzB,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;SAC1C,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAAiB;QAK3C,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAE9D,IAAI,CAAC;YACH,mCAAmC;YACnC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;YAEpE,sBAAsB;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;YACxD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;YAC3E,CAAC;YAED,8CAA8C;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YAExE,qCAAqC;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEjF,4CAA4C;YAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;YAE/F,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACrC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;gBAC5B,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;aAC3C,CAAC,CAAC;YAEH,mBAAmB;YACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAE/D,uCAAuC;YACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAEnE,2CAA2C;YAC3C,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAE5C,2BAA2B;YAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAEtD,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAC3C,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAa,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;gBACpE,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;aAClE,CAAC,CAAC;YAEH,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAEpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAAiB,EAAE,OAAqB;QAI7E,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QAEjD,MAAM,cAAc,GAAG;gBACX,SAAS;;;EAGvB,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;;;;;;;;;;;CAWxC,CAAC;QAEE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;QAEzE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CACnD,IAAI,CAAC,mBAAmB,EACxB,KAAK,EACL,KAAK,CACN,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC5B,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;SAC9B,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,mBAAmB,CAAC,OAAqB;QAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QACpF,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnF,OAAO;;EAET,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE;;;EAGvC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,iBAAiB,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAG/F,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,UAAU,CAAC,CAAC,SAAS,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGpF,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;CAC5G,CAAC;IACA,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,IAAmB,EACnB,OAAqB,EACrB,OAAqB;QAErB,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC3D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC5D,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAEtE,MAAM,aAAa,GAAG;;;QAGlB,IAAI,CAAC,KAAK,CAAC,MAAM;WACd,YAAY,gBAAgB,YAAY;YACvC,aAAa;;;EAGvB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;CAG3H,CAAC;QAEE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACpD,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0DAA0D,EAAE;YACvF,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;SACzC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAEO,6BAA6B,CAAC,OAAqB;QACzD,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC1C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,QAAQ,MAAM,CAAC,MAAM,wBAAwB,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,OAAO,QAAQ,MAAM,CAAC,MAAM,YAAY,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC;YACrF,CAAC;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC5B,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,uBAAuB,cAAc,EAAE;SACjD,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,IAAmB,EACnB,OAAqB,EACrB,OAAqB;QAErB,6BAA6B;QAC7B,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEpD,uCAAuC;QACvC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;gBACvD,OAAO,IAAI,EAAE,IAAI,CAAC;YACpB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEhB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;QAED,sBAAsB;QACtB,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,cAAc,CAAC,SAAS,CAC3B,QAAQ,IAAI,CAAC,IAAI,YAAY,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EACrD,OAAO,EACP,CAAC,EACD,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CACvB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,SAAS,CAC3B,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,WAAW,eAAe,CAAC,MAAM,gBAAgB,WAAW,CAAC,MAAM,SAAS,EACzG,MAAM,EACN,CAAC,EACD,CAAC,WAAW,EAAE,SAAS,CAAC,CACzB,CAAC;QAEF,iBAAiB;QACjB,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC;YAC7C,OAAO,EAAE;gBACP,GAAG,OAAO,CAAC,OAAO;gBAClB;oBACE,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,OAAO,EAAE,mBAAmB,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ;oBACrD,MAAM,EAAE;wBACN,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,OAAO,EAAE,WAAW,CAAC,MAAM,KAAK,CAAC;wBACjC,MAAM,EAAE,EAAE,YAAY,EAAE,eAAe,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC,MAAM,EAAE;wBAClF,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;wBACzD,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB;oBACD,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE;iBACtF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAAkB,EAAE,OAAqB;QACnE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAEpG,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,MAAM,EAAE,UAAU,CAAC,IAAI;gBACvB,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBACjE,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChE,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,sBAAsB;QAC3B,OAAO,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAEM,wBAAwB;QAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,OAAgC;QAC7E,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAEpE,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QACxD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QAEjD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAC5C,IAAI,CAAC,mBAAmB,EACxB,KAAK,EACL,OAAO,CACR,CAAC;QAEF,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,WAAW,GAAuB,IAAI,CAAC;QAE3C,IAAI,CAAC;YACH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;gBACpC,YAAY,IAAI,KAAK,CAAC;YACxB,CAAC;YAED,0CAA0C;YAC1C,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAChC,WAAW,GAAG,MAAM,CAAC,KAAoB,CAAC;YAC5C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,8BAA8B;YAC9B,WAAW,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;QAC1C,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;gBAC5B,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA7WD,0CA6WC"}