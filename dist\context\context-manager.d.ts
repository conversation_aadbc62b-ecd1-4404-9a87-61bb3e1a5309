import { AgentContext, ProjectStructure, MemoryEntry, PatternMemory } from '@/types';
export declare class ContextManager {
    private static instance;
    private projectIndexer;
    private currentContext;
    private constructor();
    static getInstance(): ContextManager;
    initializeContext(sessionId: string, workingDirectory?: string): Promise<AgentContext>;
    getCurrentContext(): AgentContext | null;
    updateContext(updates: Partial<AgentContext>): Promise<AgentContext>;
    refreshProjectStructure(): Promise<ProjectStructure>;
    addMemory(content: string, type: MemoryEntry['type'], importance?: number, tags?: string[]): void;
    addPattern(pattern: string, success: boolean): void;
    getRelevantMemories(query: string, limit?: number): MemoryEntry[];
    getSuccessfulPatterns(): PatternMemory[];
    getContextSummary(): string;
    private getEnvironmentInfo;
    private initializeMemory;
    analyzeProjectChanges(): Promise<{
        newFiles: string[];
        modifiedFiles: string[];
        deletedFiles: string[];
    }>;
    getWorkingDirectory(): string;
    getProjectType(): string;
    hasGitRepository(): boolean;
    getDependencies(): string[];
}
//# sourceMappingURL=context-manager.d.ts.map