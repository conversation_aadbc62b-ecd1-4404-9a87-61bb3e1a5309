import { LL<PERSON>rovider, LLMProviderConfig } from './llm-provider';
import { LLMMessage, LLMResponse } from '@/types';
export declare class AnthropicProvider extends LLMProvider {
    private client;
    constructor(config: LLMProviderConfig);
    generateResponse(messages: LLMMessage[], tools?: any[], stream?: boolean): Promise<LLMResponse>;
    generateStream(messages: LLMMessage[], tools?: any[], onChunk?: (chunk: string) => void): AsyncGenerator<string, LLMResponse, unknown>;
    validateConnection(): Promise<boolean>;
    private formatAnthropicMessages;
    private formatAnthropicTools;
}
//# sourceMappingURL=anthropic.d.ts.map