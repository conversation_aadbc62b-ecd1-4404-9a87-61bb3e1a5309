"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderFactory = void 0;
const deepseek_1 = require("./deepseek");
const openai_1 = require("./openai");
const anthropic_1 = require("./anthropic");
class ProviderFactory {
    static createProvider(type, config) {
        switch (type) {
            case 'deepseek':
                return new deepseek_1.DeepSeekProvider(config);
            case 'openai':
                return new openai_1.OpenAIProvider(config);
            case 'anthropic':
                return new anthropic_1.AnthropicProvider(config);
            case 'ollama':
                // TODO: Implement OllamaProvider
                throw new Error('Ollama provider not yet implemented');
            case 'gemini':
                // TODO: Implement GeminiProvider
                throw new Error('Gemini provider not yet implemented');
            case 'mistral':
                // TODO: Implement MistralProvider
                throw new Error('Mistral provider not yet implemented');
            default:
                throw new Error(`Unsupported provider: ${type}`);
        }
    }
    static getSupportedProviders() {
        return ['deepseek', 'openai', 'anthropic'];
    }
    static getProviderModels(provider) {
        switch (provider) {
            case 'deepseek':
                return [
                    'deepseek-chat',
                    'deepseek-coder',
                    'deepseek-reasoner',
                ];
            case 'openai':
                return [
                    'gpt-4o',
                    'gpt-4o-mini',
                    'gpt-4-turbo',
                    'gpt-4',
                    'gpt-3.5-turbo',
                ];
            case 'anthropic':
                return [
                    'claude-3-5-sonnet-20241022',
                    'claude-3-5-haiku-20241022',
                    'claude-3-opus-20240229',
                    'claude-3-sonnet-20240229',
                    'claude-3-haiku-20240307',
                ];
            case 'ollama':
                return [
                    'llama3.2',
                    'llama3.1',
                    'codellama',
                    'mistral',
                    'qwen2.5-coder',
                ];
            case 'gemini':
                return [
                    'gemini-1.5-pro',
                    'gemini-1.5-flash',
                    'gemini-1.0-pro',
                ];
            case 'mistral':
                return [
                    'mistral-large-latest',
                    'mistral-medium-latest',
                    'mistral-small-latest',
                    'codestral-latest',
                ];
            default:
                return [];
        }
    }
    static getDefaultModel(provider) {
        const models = this.getProviderModels(provider);
        return models[0] || '';
    }
    static validateProviderConfig(provider, config) {
        if (!config.apiKey && provider !== 'ollama') {
            return false;
        }
        if (!config.model) {
            return false;
        }
        const supportedModels = this.getProviderModels(provider);
        if (supportedModels.length > 0 && !supportedModels.includes(config.model)) {
            return false;
        }
        return true;
    }
}
exports.ProviderFactory = ProviderFactory;
__exportStar(require("./llm-provider"), exports);
__exportStar(require("./deepseek"), exports);
__exportStar(require("./openai"), exports);
__exportStar(require("./anthropic"), exports);
//# sourceMappingURL=index.js.map