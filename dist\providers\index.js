import { DeepSeekProvider } from './deepseek';
import { OpenAIProvider } from './openai';
import { AnthropicProvider } from './anthropic';
export class ProviderFactory {
    static createProvider(type, config) {
        switch (type) {
            case 'deepseek':
                return new DeepSeekProvider(config);
            case 'openai':
                return new OpenAIProvider(config);
            case 'anthropic':
                return new AnthropicProvider(config);
            case 'ollama':
                // TODO: Implement OllamaProvider
                throw new Error('Ollama provider not yet implemented');
            case 'gemini':
                // TODO: Implement GeminiProvider
                throw new Error('Gemini provider not yet implemented');
            case 'mistral':
                // TODO: Implement MistralProvider
                throw new Error('Mistral provider not yet implemented');
            default:
                throw new Error(`Unsupported provider: ${type}`);
        }
    }
    static getSupportedProviders() {
        return ['deepseek', 'openai', 'anthropic'];
    }
    static getProviderModels(provider) {
        switch (provider) {
            case 'deepseek':
                return [
                    'deepseek-chat',
                    'deepseek-coder',
                    'deepseek-reasoner',
                ];
            case 'openai':
                return [
                    'gpt-4o',
                    'gpt-4o-mini',
                    'gpt-4-turbo',
                    'gpt-4',
                    'gpt-3.5-turbo',
                ];
            case 'anthropic':
                return [
                    'claude-3-5-sonnet-20241022',
                    'claude-3-5-haiku-20241022',
                    'claude-3-opus-20240229',
                    'claude-3-sonnet-20240229',
                    'claude-3-haiku-20240307',
                ];
            case 'ollama':
                return [
                    'llama3.2',
                    'llama3.1',
                    'codellama',
                    'mistral',
                    'qwen2.5-coder',
                ];
            case 'gemini':
                return [
                    'gemini-1.5-pro',
                    'gemini-1.5-flash',
                    'gemini-1.0-pro',
                ];
            case 'mistral':
                return [
                    'mistral-large-latest',
                    'mistral-medium-latest',
                    'mistral-small-latest',
                    'codestral-latest',
                ];
            default:
                return [];
        }
    }
    static getDefaultModel(provider) {
        const models = this.getProviderModels(provider);
        return models[0] || '';
    }
    static validateProviderConfig(provider, config) {
        if (!config.apiKey && provider !== 'ollama') {
            return false;
        }
        if (!config.model) {
            return false;
        }
        const supportedModels = this.getProviderModels(provider);
        if (supportedModels.length > 0 && !supportedModels.includes(config.model)) {
            return false;
        }
        return true;
    }
}
export * from './llm-provider';
export * from './deepseek';
export * from './openai';
export * from './anthropic';
//# sourceMappingURL=index.js.map