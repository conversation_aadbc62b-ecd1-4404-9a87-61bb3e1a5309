import { Tool, Too<PERSON><PERSON><PERSON><PERSON>, Agent<PERSON>ontext, ToolCategory, RiskLevel } from '@/types';
export declare class ToolRegistry {
    private static instance;
    private tools;
    private categories;
    private constructor();
    static getInstance(): ToolRegistry;
    private registerBuiltinTools;
    registerTool(tool: Tool): void;
    getTool(name: string): Tool | undefined;
    getAllTools(): Tool[];
    getToolsByCategory(category: ToolCategory): Tool[];
    getToolsByRiskLevel(riskLevel: RiskLevel): Tool[];
    getParallelTools(): Tool[];
    executeTool(toolName: string, parameters: Record<string, any>, context: AgentContext): Promise<ToolResult>;
    executeToolsParallel(executions: Array<{
        toolName: string;
        parameters: Record<string, any>;
    }>, context: AgentContext, maxConcurrency?: number): Promise<ToolResult[]>;
    private validateParameters;
    private validateParameterType;
    getToolsForLLM(): any[];
    getToolNames(): string[];
    hasRiskyTools(toolNames: string[]): boolean;
}
//# sourceMappingURL=tool-registry.d.ts.map