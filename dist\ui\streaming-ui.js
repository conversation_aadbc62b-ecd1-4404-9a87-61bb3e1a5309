"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamingUI = void 0;
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const events_1 = require("events");
class StreamingUI extends events_1.EventEmitter {
    config;
    spinners = new Map();
    currentPlan = null;
    startTime = null;
    constructor(config = {
        theme: 'auto',
        verbose: false,
        showProgress: true,
        showTimestamps: true,
        maxOutputLines: 50,
    }) {
        super();
        this.config = config;
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.on('task_start', this.handleTaskStart.bind(this));
        this.on('task_progress', this.handleTaskProgress.bind(this));
        this.on('task_complete', this.handleTaskComplete.bind(this));
        this.on('task_error', this.handleTaskError.bind(this));
        this.on('plan_update', this.handlePlanUpdate.bind(this));
        this.on('output', this.handleOutput.bind(this));
    }
    emitEvent(event) {
        this.emit(event.type, event);
    }
    startExecution(plan) {
        this.currentPlan = plan;
        this.startTime = new Date();
        this.displayHeader();
        this.displayPlanSummary(plan);
        this.emitEvent({
            type: 'plan_update',
            data: { plan, status: 'started' },
            timestamp: new Date(),
        });
    }
    completeExecution(results) {
        this.clearAllSpinners();
        this.displayExecutionSummary(results);
        this.emitEvent({
            type: 'plan_update',
            data: { results, status: 'completed' },
            timestamp: new Date(),
        });
    }
    displayHeader() {
        const title = chalk_1.default.bold.blue('🤖 AI CLI Agent - Autonomous Execution');
        const separator = chalk_1.default.gray('─'.repeat(60));
        console.log('\n' + title);
        console.log(separator);
        if (this.config.showTimestamps && this.startTime) {
            console.log(chalk_1.default.gray(`Started: ${this.startTime.toLocaleTimeString()}`));
        }
    }
    displayPlanSummary(plan) {
        console.log(chalk_1.default.bold('\n📋 Execution Plan:'));
        console.log(chalk_1.default.gray(`  Tasks: ${plan.tasks.length}`));
        console.log(chalk_1.default.gray(`  Parallel Groups: ${plan.parallelGroups.length}`));
        console.log(chalk_1.default.gray(`  Estimated Duration: ${this.formatDuration(plan.estimatedDuration)}`));
        if (this.config.verbose) {
            console.log(chalk_1.default.bold('\n📝 Task Details:'));
            plan.tasks.forEach((task, index) => {
                const priority = this.getPriorityIcon(task.priority);
                console.log(chalk_1.default.gray(`  ${index + 1}. ${priority} ${task.tool} - ${task.description}`));
            });
        }
        console.log('');
    }
    handleTaskStart(event) {
        const { taskId, taskName, tool } = event.data;
        if (this.config.showProgress) {
            const spinner = (0, ora_1.default)({
                text: chalk_1.default.blue(`${tool}: ${taskName}`),
                color: 'blue',
            }).start();
            this.spinners.set(taskId, spinner);
        }
        if (this.config.verbose) {
            const timestamp = this.config.showTimestamps ? chalk_1.default.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
            console.log(`${timestamp}${chalk_1.default.blue('▶')} Starting: ${chalk_1.default.bold(tool)}`);
        }
    }
    handleTaskProgress(event) {
        const { taskId, progress, message } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner && this.config.showProgress) {
            spinner.text = chalk_1.default.blue(`${message} ${this.getProgressBar(progress)}`);
        }
        if (this.config.verbose) {
            const timestamp = this.config.showTimestamps ? chalk_1.default.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
            console.log(`${timestamp}${chalk_1.default.yellow('⚡')} ${message}`);
        }
    }
    handleTaskComplete(event) {
        const { taskId, taskName, tool, duration, output } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner) {
            spinner.succeed(chalk_1.default.green(`${tool}: ${taskName} ${chalk_1.default.gray(`(${this.formatDuration(duration)})`)}`));
            this.spinners.delete(taskId);
        }
        if (this.config.verbose && output) {
            this.displayTaskOutput(output);
        }
    }
    handleTaskError(event) {
        const { taskId, taskName, tool, error, duration } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner) {
            spinner.fail(chalk_1.default.red(`${tool}: ${taskName} ${chalk_1.default.gray(`(${this.formatDuration(duration)})`)}`));
            this.spinners.delete(taskId);
        }
        console.log(chalk_1.default.red(`  ❌ Error: ${error.message}`));
        if (this.config.verbose && error.stack) {
            console.log(chalk_1.default.gray(`  Stack: ${error.stack.split('\n')[1]?.trim()}`));
        }
    }
    handlePlanUpdate(event) {
        const { status, progress } = event.data;
        if (status === 'progress' && progress) {
            this.displayOverallProgress(progress);
        }
    }
    handleOutput(event) {
        const { content, type } = event.data;
        if (type === 'stream') {
            process.stdout.write(content);
        }
        else {
            console.log(content);
        }
    }
    displayTaskOutput(output) {
        if (typeof output === 'string') {
            const lines = output.split('\n').slice(0, this.config.maxOutputLines);
            lines.forEach(line => {
                console.log(chalk_1.default.gray(`    ${line}`));
            });
            if (output.split('\n').length > this.config.maxOutputLines) {
                console.log(chalk_1.default.gray(`    ... (${output.split('\n').length - this.config.maxOutputLines} more lines)`));
            }
        }
        else if (typeof output === 'object') {
            console.log(chalk_1.default.gray(`    ${JSON.stringify(output, null, 2)}`));
        }
    }
    displayOverallProgress(progress) {
        const progressBar = this.getProgressBar(progress.percentage);
        const status = `${progress.completed}/${progress.total} tasks completed`;
        console.log(`\n${chalk_1.default.bold('Progress:')} ${progressBar} ${chalk_1.default.gray(status)}\n`);
    }
    displayExecutionSummary(results) {
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        console.log(chalk_1.default.bold('\n📊 Execution Summary:'));
        console.log(chalk_1.default.green(`  ✅ Successful: ${successful}`));
        if (failed > 0) {
            console.log(chalk_1.default.red(`  ❌ Failed: ${failed}`));
        }
        console.log(chalk_1.default.gray(`  ⏱️  Total Duration: ${this.formatDuration(totalDuration)}`));
        if (this.startTime) {
            const endTime = new Date();
            const wallClockTime = endTime.getTime() - this.startTime.getTime();
            console.log(chalk_1.default.gray(`  🕐 Wall Clock Time: ${this.formatDuration(wallClockTime)}`));
        }
        // Display failed tasks
        if (failed > 0) {
            console.log(chalk_1.default.bold('\n❌ Failed Tasks:'));
            results.filter(r => !r.success).forEach(result => {
                console.log(chalk_1.default.red(`  • ${result.taskId}: ${result.error?.message || 'Unknown error'}`));
            });
        }
        console.log('');
    }
    clearAllSpinners() {
        this.spinners.forEach(spinner => {
            spinner.stop();
        });
        this.spinners.clear();
    }
    getProgressBar(percentage) {
        const width = 20;
        const filled = Math.round((percentage / 100) * width);
        const empty = width - filled;
        return chalk_1.default.green('█'.repeat(filled)) + chalk_1.default.gray('░'.repeat(empty)) + chalk_1.default.gray(` ${percentage.toFixed(1)}%`);
    }
    getPriorityIcon(priority) {
        if (priority >= 8)
            return chalk_1.default.red('🔴');
        if (priority >= 6)
            return chalk_1.default.yellow('🟡');
        if (priority >= 4)
            return chalk_1.default.blue('🔵');
        return chalk_1.default.gray('⚪');
    }
    formatDuration(ms) {
        if (ms < 1000)
            return `${ms}ms`;
        if (ms < 60000)
            return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    }
    setConfig(config) {
        this.config = { ...this.config, ...config };
    }
    getConfig() {
        return { ...this.config };
    }
    streamLLMResponse(onChunk) {
        // Set up streaming for LLM responses
        const streamChunk = (chunk) => {
            if (this.config.verbose) {
                process.stdout.write(chalk_1.default.cyan(chunk));
            }
            onChunk(chunk);
        };
        return streamChunk;
    }
    displayThinking(message) {
        return (0, ora_1.default)({
            text: chalk_1.default.magenta(`🤔 ${message}`),
            color: 'magenta',
        }).start();
    }
    displaySuccess(message) {
        console.log(chalk_1.default.green(`✅ ${message}`));
    }
    displayError(message) {
        console.log(chalk_1.default.red(`❌ ${message}`));
    }
    displayWarning(message) {
        console.log(chalk_1.default.yellow(`⚠️  ${message}`));
    }
    displayInfo(message) {
        console.log(chalk_1.default.blue(`ℹ️  ${message}`));
    }
    prompt(message) {
        console.log(chalk_1.default.cyan(`❓ ${message}`));
    }
    clear() {
        console.clear();
    }
    newLine() {
        console.log('');
    }
}
exports.StreamingUI = StreamingUI;
//# sourceMappingURL=streaming-ui.js.map