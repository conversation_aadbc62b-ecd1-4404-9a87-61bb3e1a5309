import chalk from 'chalk';
import ora from 'ora';
import { EventEmitter } from 'events';
export class StreamingUI extends EventEmitter {
    config;
    spinners = new Map();
    currentPlan = null;
    startTime = null;
    constructor(config = {
        theme: 'auto',
        verbose: false,
        showProgress: true,
        showTimestamps: true,
        maxOutputLines: 50,
    }) {
        super();
        this.config = config;
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.on('task_start', this.handleTaskStart.bind(this));
        this.on('task_progress', this.handleTaskProgress.bind(this));
        this.on('task_complete', this.handleTaskComplete.bind(this));
        this.on('task_error', this.handleTaskError.bind(this));
        this.on('plan_update', this.handlePlanUpdate.bind(this));
        this.on('output', this.handleOutput.bind(this));
    }
    emitEvent(event) {
        this.emit(event.type, event);
    }
    startExecution(plan) {
        this.currentPlan = plan;
        this.startTime = new Date();
        this.displayHeader();
        this.displayPlanSummary(plan);
        this.emitEvent({
            type: 'plan_update',
            data: { plan, status: 'started' },
            timestamp: new Date(),
        });
    }
    completeExecution(results) {
        this.clearAllSpinners();
        this.displayExecutionSummary(results);
        this.emitEvent({
            type: 'plan_update',
            data: { results, status: 'completed' },
            timestamp: new Date(),
        });
    }
    displayHeader() {
        const title = chalk.bold.blue('🤖 AI CLI Agent - Autonomous Execution');
        const separator = chalk.gray('─'.repeat(60));
        console.log('\n' + title);
        console.log(separator);
        if (this.config.showTimestamps && this.startTime) {
            console.log(chalk.gray(`Started: ${this.startTime.toLocaleTimeString()}`));
        }
    }
    displayPlanSummary(plan) {
        console.log(chalk.bold('\n📋 Execution Plan:'));
        console.log(chalk.gray(`  Tasks: ${plan.tasks.length}`));
        console.log(chalk.gray(`  Parallel Groups: ${plan.parallelGroups.length}`));
        console.log(chalk.gray(`  Estimated Duration: ${this.formatDuration(plan.estimatedDuration)}`));
        if (this.config.verbose) {
            console.log(chalk.bold('\n📝 Task Details:'));
            plan.tasks.forEach((task, index) => {
                const priority = this.getPriorityIcon(task.priority);
                console.log(chalk.gray(`  ${index + 1}. ${priority} ${task.tool} - ${task.description}`));
            });
        }
        console.log('');
    }
    handleTaskStart(event) {
        const { taskId, taskName, tool } = event.data;
        if (this.config.showProgress) {
            const spinner = ora({
                text: chalk.blue(`${tool}: ${taskName}`),
                color: 'blue',
            }).start();
            this.spinners.set(taskId, spinner);
        }
        if (this.config.verbose) {
            const timestamp = this.config.showTimestamps ? chalk.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
            console.log(`${timestamp}${chalk.blue('▶')} Starting: ${chalk.bold(tool)}`);
        }
    }
    handleTaskProgress(event) {
        const { taskId, progress, message } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner && this.config.showProgress) {
            spinner.text = chalk.blue(`${message} ${this.getProgressBar(progress)}`);
        }
        if (this.config.verbose) {
            const timestamp = this.config.showTimestamps ? chalk.gray(`[${event.timestamp.toLocaleTimeString()}] `) : '';
            console.log(`${timestamp}${chalk.yellow('⚡')} ${message}`);
        }
    }
    handleTaskComplete(event) {
        const { taskId, taskName, tool, duration, output } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner) {
            spinner.succeed(chalk.green(`${tool}: ${taskName} ${chalk.gray(`(${this.formatDuration(duration)})`)}`));
            this.spinners.delete(taskId);
        }
        if (this.config.verbose && output) {
            this.displayTaskOutput(output);
        }
    }
    handleTaskError(event) {
        const { taskId, taskName, tool, error, duration } = event.data;
        const spinner = this.spinners.get(taskId);
        if (spinner) {
            spinner.fail(chalk.red(`${tool}: ${taskName} ${chalk.gray(`(${this.formatDuration(duration)})`)}`));
            this.spinners.delete(taskId);
        }
        console.log(chalk.red(`  ❌ Error: ${error.message}`));
        if (this.config.verbose && error.stack) {
            console.log(chalk.gray(`  Stack: ${error.stack.split('\n')[1]?.trim()}`));
        }
    }
    handlePlanUpdate(event) {
        const { status, progress } = event.data;
        if (status === 'progress' && progress) {
            this.displayOverallProgress(progress);
        }
    }
    handleOutput(event) {
        const { content, type } = event.data;
        if (type === 'stream') {
            process.stdout.write(content);
        }
        else {
            console.log(content);
        }
    }
    displayTaskOutput(output) {
        if (typeof output === 'string') {
            const lines = output.split('\n').slice(0, this.config.maxOutputLines);
            lines.forEach(line => {
                console.log(chalk.gray(`    ${line}`));
            });
            if (output.split('\n').length > this.config.maxOutputLines) {
                console.log(chalk.gray(`    ... (${output.split('\n').length - this.config.maxOutputLines} more lines)`));
            }
        }
        else if (typeof output === 'object') {
            console.log(chalk.gray(`    ${JSON.stringify(output, null, 2)}`));
        }
    }
    displayOverallProgress(progress) {
        const progressBar = this.getProgressBar(progress.percentage);
        const status = `${progress.completed}/${progress.total} tasks completed`;
        console.log(`\n${chalk.bold('Progress:')} ${progressBar} ${chalk.gray(status)}\n`);
    }
    displayExecutionSummary(results) {
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        console.log(chalk.bold('\n📊 Execution Summary:'));
        console.log(chalk.green(`  ✅ Successful: ${successful}`));
        if (failed > 0) {
            console.log(chalk.red(`  ❌ Failed: ${failed}`));
        }
        console.log(chalk.gray(`  ⏱️  Total Duration: ${this.formatDuration(totalDuration)}`));
        if (this.startTime) {
            const endTime = new Date();
            const wallClockTime = endTime.getTime() - this.startTime.getTime();
            console.log(chalk.gray(`  🕐 Wall Clock Time: ${this.formatDuration(wallClockTime)}`));
        }
        // Display failed tasks
        if (failed > 0) {
            console.log(chalk.bold('\n❌ Failed Tasks:'));
            results.filter(r => !r.success).forEach(result => {
                console.log(chalk.red(`  • ${result.taskId}: ${result.error?.message || 'Unknown error'}`));
            });
        }
        console.log('');
    }
    clearAllSpinners() {
        this.spinners.forEach(spinner => {
            spinner.stop();
        });
        this.spinners.clear();
    }
    getProgressBar(percentage) {
        const width = 20;
        const filled = Math.round((percentage / 100) * width);
        const empty = width - filled;
        return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty)) + chalk.gray(` ${percentage.toFixed(1)}%`);
    }
    getPriorityIcon(priority) {
        if (priority >= 8)
            return chalk.red('🔴');
        if (priority >= 6)
            return chalk.yellow('🟡');
        if (priority >= 4)
            return chalk.blue('🔵');
        return chalk.gray('⚪');
    }
    formatDuration(ms) {
        if (ms < 1000)
            return `${ms}ms`;
        if (ms < 60000)
            return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    }
    setConfig(config) {
        this.config = { ...this.config, ...config };
    }
    getConfig() {
        return { ...this.config };
    }
    streamLLMResponse(onChunk) {
        // Set up streaming for LLM responses
        const streamChunk = (chunk) => {
            if (this.config.verbose) {
                process.stdout.write(chalk.cyan(chunk));
            }
            onChunk(chunk);
        };
        return streamChunk;
    }
    displayThinking(message) {
        return ora({
            text: chalk.magenta(`🤔 ${message}`),
            color: 'magenta',
        }).start();
    }
    displaySuccess(message) {
        console.log(chalk.green(`✅ ${message}`));
    }
    displayError(message) {
        console.log(chalk.red(`❌ ${message}`));
    }
    displayWarning(message) {
        console.log(chalk.yellow(`⚠️  ${message}`));
    }
    displayInfo(message) {
        console.log(chalk.blue(`ℹ️  ${message}`));
    }
    prompt(message) {
        console.log(chalk.cyan(`❓ ${message}`));
    }
    clear() {
        console.clear();
    }
    newLine() {
        console.log('');
    }
}
//# sourceMappingURL=streaming-ui.js.map