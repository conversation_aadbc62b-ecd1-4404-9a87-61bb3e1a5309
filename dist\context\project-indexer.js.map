{"version": 3, "file": "project-indexer.js", "sourceRoot": "", "sources": ["../../src/context/project-indexer.ts"], "names": [], "mappings": ";;;;;;AACA,wDAA0B;AAC1B,gDAAwB;AACxB,0DAA6B;AAC7B,iCAA8B;AAC9B,2CAAwC;AAExC,MAAa,cAAc;IACR,eAAe,GAAG;QACjC,iBAAiB;QACjB,SAAS;QACT,SAAS;QACT,UAAU;QACV,WAAW;QACX,UAAU;QACV,UAAU;QACV,aAAa;QACb,OAAO;QACP,WAAW;QACX,WAAW;QACX,OAAO;QACP,QAAQ;KACT,CAAC;IAEK,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAEtE,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YACjC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;SACnC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAqB;YAClC,IAAI,EAAE,YAAY;YAClB,KAAK;YACL,WAAW;YACX,OAAO;YACP,WAAW;YACX,YAAY;SACb,CAAC;QAEF,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,SAAS,EAAE,KAAK,CAAC,MAAM;YACvB,cAAc,EAAE,WAAW,CAAC,MAAM;YAClC,MAAM,EAAE,CAAC,CAAC,OAAO;YACjB,UAAU,EAAE,CAAC,CAAC,WAAW;YACzB,eAAe,EAAE,YAAY,CAAC,MAAM;SACrC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAC;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE;gBACjC,GAAG,EAAE,QAAQ;gBACb,MAAM,EAAE,IAAI,CAAC,eAAe;gBAC5B,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YAEH,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,kBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAEpD,MAAM,QAAQ,GAAa;wBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC9B,SAAS,EAAE,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;wBAClC,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,YAAY,EAAE,KAAK,CAAC,KAAK;wBACzB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;wBACrF,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;qBACpC,CAAC;oBAEF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC3B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,MAAM,WAAW,GAAG,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE;gBACvC,GAAG,EAAE,QAAQ;gBACb,MAAM,EAAE,IAAI,CAAC,eAAe;gBAC5B,eAAe,EAAE,IAAI;gBACrB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAoB,EAAE,CAAC;YAE3C,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAC9B,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;oBACzC,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC3C,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAErC,MAAM,aAAa,GAAkB;wBACnC,IAAI,EAAE,GAAG;wBACT,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACxB,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAC1D,IAAI,EAAE,QAAQ,CAAC,MAAM;qBACtB,CAAC;oBAEF,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACrC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,6BAA6B,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC3C,IAAI,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBACxF,IAAA,aAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;gBAC7D,IAAA,aAAK,EAAC,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;gBACtD,IAAA,aAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;gBAChE,IAAA,aAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;aAC3D,CAAC,CAAC;YAEH,MAAM,OAAO,GAAY;gBACvB,MAAM,EAAE,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBAC1F,MAAM,EAAE,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBAC1F,MAAM,EAAE,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBAC1F,MAAM,EAAE,YAAY,CAAC,MAAM,KAAK,WAAW;oBACzC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACnE,CAAC,CAAC,EAAE;aACP,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,8BAA8B;QAC9B,MAAM,YAAY,GAAG;YACnB,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,KAAc,EAAE;YAC9C,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,QAAiB,EAAE;YACnD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,QAAiB,EAAE;YACrD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAe,EAAE;YAC7C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAa,EAAE;YACvC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAgB,EAAE;SAClD,CAAC;QAEF,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,YAAY,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE3C,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;wBAC5B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBAC/C,CAAC;yBAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;wBACrC,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;oBACjD,CAAC;yBAAM,IAAI,IAAI,KAAK,kBAAkB,EAAE,CAAC;wBACvC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;oBACnD,CAAC;yBAAM,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;wBACjC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC7C,CAAC;yBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC7B,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,SAAS;YAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO;YACnC,IAAI,EAAE,KAAK;YACX,YAAY,EAAE;gBACZ,GAAG,OAAO,CAAC,YAAY;gBACvB,GAAG,OAAO,CAAC,eAAe;aAC3B;YACD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;SAC/B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC/C,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEpD,sCAAsC;QACtC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE9D,OAAO;YACL,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;YACjC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO;YACrC,IAAI,EAAE,QAAQ;YACd,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACjD,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAEvF,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAClD,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,QAAQ;YACd,YAAY;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEpD,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE9D,OAAO;YACL,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;YACjC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO;YACrC,IAAI,EAAE,MAAM;YACZ,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEpD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAE3C,OAAO;YACL,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;YACnC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO;YAChC,IAAI,EAAE,IAAI;YACV,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,MAAM,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;QAE5C,oCAAoC;QACpC,MAAM,eAAe,GAAG;YACtB,cAAc;YACd,kBAAkB;YAClB,YAAY;YACZ,QAAQ;YACR,eAAe;SAChB,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC3C,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,MAAM,SAAS,GAAG;YAChB,mBAAmB;YACnB,WAAW;YACX,gBAAgB;YAChB,aAAa;YACb,YAAY;YACZ,QAAQ;YACR,eAAe;SAChB,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC3C,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;CACF;AA5TD,wCA4TC"}