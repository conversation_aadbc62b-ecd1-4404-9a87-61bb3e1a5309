{"version": 3, "file": "context-manager.js", "sourceRoot": "", "sources": ["../../src/context/context-manager.ts"], "names": [], "mappings": ";;;;;;AACA,uDAAmD;AACnD,2CAAwC;AACxC,mCAAgC;AAChC,gDAAwB;AACxB,4CAAoB;AAEpB,MAAa,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,cAAc,CAAiB;IAC/B,cAAc,GAAwB,IAAI,CAAC;IAEnD;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,EAAE,CAAC;IAC7C,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,SAAiB,EACjB,mBAA2B,OAAO,CAAC,GAAG,EAAE;QAExC,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAErE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAClF,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEvC,IAAI,CAAC,cAAc,GAAG;YACpB,SAAS;YACT,gBAAgB,EAAE,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAChD,gBAAgB;YAChB,WAAW,EAAE,eAAe;YAC5B,OAAO,EAAE,EAAE;YACX,MAAM;SACP,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACjC,SAAS;YACT,WAAW,EAAE,gBAAgB,CAAC,IAAI;YAClC,SAAS,EAAE,gBAAgB,CAAC,KAAK,CAAC,MAAM;YACxC,cAAc,EAAE,gBAAgB,CAAC,WAAW,CAAC,MAAM;SACpD,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAA8B;QACvD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,cAAc,GAAG;YACpB,GAAG,IAAI,CAAC,cAAc;YACtB,GAAG,OAAO;SACX,CAAC;QAEF,gDAAgD;QAChD,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;YAClG,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1G,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,uBAAuB;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAClG,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,YAAY,CAAC;QAEpD,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM;YACpC,cAAc,EAAE,YAAY,CAAC,WAAW,CAAC,MAAM;SAChD,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,SAAS,CAAC,OAAe,EAAE,IAAyB,EAAE,aAAqB,CAAC,EAAE,OAAiB,EAAE;QACtG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAgB;YAC1B,EAAE,EAAE,IAAA,eAAM,GAAE;YACZ,OAAO;YACP,IAAI;YACJ,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI;SACL,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAElD,wCAAwC;QACxC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS;iBACxE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;iBAC3C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEM,UAAU,CAAC,OAAe,EAAE,OAAgB;QACjD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;QAE7F,IAAI,eAAe,EAAE,CAAC;YACpB,eAAe,CAAC,SAAS,EAAE,CAAC;YAC5B,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,eAAe,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAkB;gBACnC,OAAO;gBACP,SAAS,EAAE,CAAC;gBACZ,OAAO;gBACP,QAAQ,EAAE,IAAI,IAAI,EAAE;aACrB,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACzD,CAAC;IAEM,mBAAmB,CAAC,KAAa,EAAE,QAAgB,EAAE;QAC1D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,WAAW,GAAG;YAClB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS;YACvC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ;SACvC,CAAC;QAEF,sEAAsE;QACtE,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC9C,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;YAE9B,+CAA+C;YAC/C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAElD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,KAAK,IAAI,CAAC,CAAC;gBACb,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAC3F,IAAI,gBAAgB,GAAG,CAAC;gBAAE,KAAK,IAAI,CAAC,CAAC;iBAChC,IAAI,gBAAgB,GAAG,CAAC;gBAAE,KAAK,IAAI,CAAC,CAAC;YAE1C,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc;aAClB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;aACf,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAEM,qBAAqB;QAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ;aACvC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC;aACzC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;IAC/C,CAAC;IAEM,iBAAiB;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,sBAAsB,CAAC;QAChC,CAAC;QAED,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAEtE,MAAM,OAAO,GAAG;YACd,sBAAsB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;YAC5D,iBAAiB,gBAAgB,CAAC,WAAW,EAAE,IAAI,IAAI,SAAS,EAAE;YAClE,UAAU,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE;YACzC,gBAAgB,gBAAgB,CAAC,WAAW,CAAC,MAAM,EAAE;YACrD,aAAa,WAAW,CAAC,QAAQ,KAAK,WAAW,CAAC,IAAI,GAAG;YACzD,UAAU,WAAW,CAAC,KAAK,EAAE;YAC7B,wBAAwB,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;YACjD,uBAAuB,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC/C,aAAa,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;SACtC,CAAC;QAEF,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,eAAe,gBAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/D,OAAO,CAAC,IAAI,CAAC,eAAe,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,YAAY,gBAAgB,CAAC,WAAW,CAAC,IAAI,IAAI,gBAAgB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YACtG,OAAO,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACjG,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,MAAM,GAAG,GAA2B,EAAE,CAAC;QACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,YAAE,CAAC,QAAQ,EAAE;YACvB,IAAI,EAAE,YAAE,CAAC,IAAI,EAAE;YACf,WAAW,EAAE,OAAO,CAAC,OAAO;YAC5B,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS;YAC5D,GAAG;SACJ,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,qBAAqB;QAKhC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;QAC1D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAElG,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAElE,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK;aAChC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACtC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEpB,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK;aACpC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACtC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEpB,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK;aACrC,MAAM,CAAC,OAAO,CAAC,EAAE;YAChB,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;YACtE,OAAO,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QACtF,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEpB,oCAAoC;QACpC,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,YAAY,CAAC;QAEpD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,CAAC;IACnD,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAChE,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,WAAW,EAAE,IAAI,IAAI,SAAS,CAAC;IAC9E,CAAC;IAEM,gBAAgB;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC;IACzD,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,YAAY,IAAI,EAAE,CAAC;IAClE,CAAC;CACF;AAvSD,wCAuSC"}