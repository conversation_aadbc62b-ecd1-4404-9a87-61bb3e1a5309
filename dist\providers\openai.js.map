{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/providers/openai.ts"], "names": [], "mappings": ";;;;;;AAAA,iDAAgE;AAEhE,oDAA4B;AAE5B,MAAa,cAAe,SAAQ,0BAAW;IACrC,MAAM,CAAS;IAEvB,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,QAAsB,EACtB,KAAa,EACb,SAAkB,KAAK;QAEvB,IAAI,CAAC;YACH,MAAM,MAAM,GAA2C;gBACrD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAA6C;gBACnF,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,SAAS;gBAC1B,MAAM;aACP,CAAC;YAEF,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAqC,CAAC;gBAC3E,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;YAC9B,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAQ,CAAC;YAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEnC,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;gBACrC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;gBACjG,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;oBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;oBAC1C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB;oBAClD,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;iBACzC,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,CAAC,cAAc,CACnB,QAAsB,EACtB,KAAa,EACb,OAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,MAAM,GAA2C;gBACrD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAA6C;gBACnF,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,SAAS;gBAC1B,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAqC,CAAC;gBAC3E,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;YAC9B,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEjE,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAU,EAAE,CAAC;YAC1B,IAAI,KAAU,CAAC;YAEf,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAEhC,IAAI,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;oBAC3B,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;oBACpC,IAAI,OAAO;wBAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAC3C,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC7B,CAAC;gBAED,IAAI,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;oBAC9B,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC7C,CAAC;gBAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,WAAW;gBACpB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC5E,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,aAAa;oBACjC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;oBACzC,WAAW,EAAE,KAAK,CAAC,YAAY;iBAChC,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAjHD,wCAiHC"}