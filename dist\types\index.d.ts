import { z } from 'zod';
export interface AgentConfig {
    provider: LLMProvider;
    model: string;
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
}
export interface AgentContext {
    sessionId: string;
    workingDirectory: string;
    projectStructure: ProjectStructure;
    environment: EnvironmentInfo;
    history: ExecutionHistory[];
    memory: ContextMemory;
}
export interface ExecutionPlan {
    id: string;
    tasks: Task[];
    dependencies: TaskDependency[];
    parallelGroups: ParallelGroup[];
    estimatedDuration: number;
}
export interface Task {
    id: string;
    type: TaskType;
    description: string;
    tool: string;
    parameters: Record<string, any>;
    priority: number;
    retryCount: number;
    maxRetries: number;
    timeout: number;
    dependencies: string[];
}
export interface TaskResult {
    taskId: string;
    success: boolean;
    output: any;
    error?: Error;
    duration: number;
    timestamp: Date;
}
export interface ParallelGroup {
    id: string;
    taskIds: string[];
    maxConcurrency: number;
}
export interface TaskDependency {
    taskId: string;
    dependsOn: string[];
}
export type LLMProvider = 'deepseek' | 'openai' | 'ollama' | 'anthropic' | 'gemini' | 'mistral';
export interface LLMMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCalls?: ToolCall[];
    toolCallId?: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface LLMResponse {
    content: string;
    toolCalls?: ToolCall[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export interface Tool {
    name: string;
    description: string;
    parameters: ToolParameter[];
    execute: (params: Record<string, any>, context: AgentContext) => Promise<ToolResult>;
    category: ToolCategory;
    riskLevel: RiskLevel;
    parallel: boolean;
}
export interface ToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    default?: any;
    enum?: string[];
}
export interface ToolResult {
    success: boolean;
    data: any;
    error?: string;
    metadata?: Record<string, any>;
}
export type ToolCategory = 'file' | 'shell' | 'network' | 'system' | 'analysis' | 'generation';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type TaskType = 'file_operation' | 'shell_command' | 'analysis' | 'generation' | 'network';
export interface ProjectStructure {
    root: string;
    files: FileInfo[];
    directories: DirectoryInfo[];
    gitInfo?: GitInfo;
    packageInfo?: PackageInfo;
    dependencies: string[];
}
export interface FileInfo {
    path: string;
    name: string;
    extension: string;
    size: number;
    lastModified: Date;
    type: 'file' | 'directory' | 'symlink';
    permissions: string;
}
export interface DirectoryInfo {
    path: string;
    name: string;
    children: string[];
    size: number;
}
export interface GitInfo {
    branch: string;
    commit: string;
    remote?: string;
    status: string[];
}
export interface PackageInfo {
    name: string;
    version: string;
    type: 'npm' | 'python' | 'rust' | 'go' | 'other';
    dependencies: Record<string, string>;
    scripts: Record<string, string>;
}
export interface EnvironmentInfo {
    platform: string;
    arch: string;
    nodeVersion?: string;
    pythonVersion?: string;
    shell: string;
    env: Record<string, string>;
}
export interface ExecutionHistory {
    id: string;
    timestamp: Date;
    command: string;
    result: TaskResult;
    context: Partial<AgentContext>;
}
export interface ContextMemory {
    shortTerm: MemoryEntry[];
    longTerm: MemoryEntry[];
    patterns: PatternMemory[];
}
export interface MemoryEntry {
    id: string;
    content: string;
    type: 'fact' | 'pattern' | 'preference' | 'error';
    importance: number;
    timestamp: Date;
    tags: string[];
}
export interface PatternMemory {
    pattern: string;
    frequency: number;
    success: boolean;
    lastUsed: Date;
}
export interface Session {
    id: string;
    name: string;
    created: Date;
    lastAccessed: Date;
    context: AgentContext;
    config: AgentConfig;
    persistent: boolean;
}
export interface StreamingEvent {
    type: 'task_start' | 'task_progress' | 'task_complete' | 'task_error' | 'plan_update' | 'output';
    data: any;
    timestamp: Date;
}
export interface UIConfig {
    theme: 'dark' | 'light' | 'auto';
    verbose: boolean;
    showProgress: boolean;
    showTimestamps: boolean;
    maxOutputLines: number;
}
export declare const AgentConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["deepseek", "openai", "ollama", "anthropic", "gemini", "mistral"]>;
    model: z.ZodString;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxTokens: z.ZodOptional<z.ZodNumber>;
    timeout: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    provider: "deepseek" | "openai" | "ollama" | "anthropic" | "gemini" | "mistral";
    model: string;
    temperature?: number | undefined;
    maxTokens?: number | undefined;
    timeout?: number | undefined;
}, {
    provider: "deepseek" | "openai" | "ollama" | "anthropic" | "gemini" | "mistral";
    model: string;
    temperature?: number | undefined;
    maxTokens?: number | undefined;
    timeout?: number | undefined;
}>;
export declare const TaskSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["file_operation", "shell_command", "analysis", "generation", "network"]>;
    description: z.ZodString;
    tool: z.ZodString;
    parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    priority: z.ZodNumber;
    retryCount: z.ZodDefault<z.ZodNumber>;
    maxRetries: z.ZodDefault<z.ZodNumber>;
    timeout: z.ZodDefault<z.ZodNumber>;
    dependencies: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    tool: string;
    type: "file_operation" | "shell_command" | "analysis" | "generation" | "network";
    timeout: number;
    id: string;
    description: string;
    parameters: Record<string, any>;
    priority: number;
    retryCount: number;
    maxRetries: number;
    dependencies: string[];
}, {
    tool: string;
    type: "file_operation" | "shell_command" | "analysis" | "generation" | "network";
    id: string;
    description: string;
    parameters: Record<string, any>;
    priority: number;
    timeout?: number | undefined;
    retryCount?: number | undefined;
    maxRetries?: number | undefined;
    dependencies?: string[] | undefined;
}>;
//# sourceMappingURL=index.d.ts.map