import { Session, AgentContext, AgentConfig } from '@/types';
import { ContextManager } from '@/context/context-manager';
import { logger } from '@/utils/logger';
import { nanoid } from 'nanoid';
import { Conf } from 'conf';
import path from 'path';
import fs from 'fs-extra';

export class SessionManager {
  private static instance: SessionManager;
  private config: Conf;
  private currentSession: Session | null = null;
  private contextManager: ContextManager;
  private sessionsDir: string;

  private constructor() {
    this.config = new Conf({
      projectName: 'ai-cli-agent',
      configName: 'sessions',
    });
    
    this.contextManager = ContextManager.getInstance();
    this.sessionsDir = path.join(this.config.path, '..', 'sessions');
    this.ensureSessionsDirectory();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  private ensureSessionsDirectory(): void {
    fs.ensureDirSync(this.sessionsDir);
  }

  public async createSession(
    name: string,
    agentConfig: AgentConfig,
    workingDirectory: string = process.cwd(),
    persistent: boolean = true
  ): Promise<Session> {
    const sessionId = nanoid();
    const now = new Date();

    logger.info('Creating new session', { sessionId, name, workingDirectory });

    // Initialize context for the session
    const context = await this.contextManager.initializeContext(sessionId, workingDirectory);

    const session: Session = {
      id: sessionId,
      name,
      created: now,
      lastAccessed: now,
      context,
      config: agentConfig,
      persistent,
    };

    if (persistent) {
      await this.saveSession(session);
    }

    this.currentSession = session;
    this.updateSessionsList(session);

    logger.info('Session created', { sessionId, name });
    return session;
  }

  public async loadSession(sessionId: string): Promise<Session> {
    logger.info('Loading session', { sessionId });

    const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
    
    if (!await fs.pathExists(sessionPath)) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    const sessionData = await fs.readJson(sessionPath);
    
    // Restore dates from JSON
    sessionData.created = new Date(sessionData.created);
    sessionData.lastAccessed = new Date(sessionData.lastAccessed);
    sessionData.context.history = sessionData.context.history.map((entry: any) => ({
      ...entry,
      timestamp: new Date(entry.timestamp),
      result: {
        ...entry.result,
        timestamp: new Date(entry.result.timestamp),
      },
    }));

    // Restore memory timestamps
    sessionData.context.memory.shortTerm = sessionData.context.memory.shortTerm.map((entry: any) => ({
      ...entry,
      timestamp: new Date(entry.timestamp),
    }));
    sessionData.context.memory.longTerm = sessionData.context.memory.longTerm.map((entry: any) => ({
      ...entry,
      timestamp: new Date(entry.timestamp),
    }));
    sessionData.context.memory.patterns = sessionData.context.memory.patterns.map((pattern: any) => ({
      ...pattern,
      lastUsed: new Date(pattern.lastUsed),
    }));

    const session: Session = sessionData;
    session.lastAccessed = new Date();

    // Update context manager with loaded context
    await this.contextManager.updateContext(session.context);

    this.currentSession = session;
    
    if (session.persistent) {
      await this.saveSession(session);
      this.updateSessionsList(session);
    }

    logger.info('Session loaded', { sessionId, name: session.name });
    return session;
  }

  public async saveSession(session: Session): Promise<void> {
    if (!session.persistent) {
      return;
    }

    const sessionPath = path.join(this.sessionsDir, `${session.id}.json`);
    
    // Update last accessed time
    session.lastAccessed = new Date();
    
    await fs.writeJson(sessionPath, session, { spaces: 2 });
    logger.debug('Session saved', { sessionId: session.id });
  }

  public async deleteSession(sessionId: string): Promise<void> {
    logger.info('Deleting session', { sessionId });

    const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
    
    if (await fs.pathExists(sessionPath)) {
      await fs.remove(sessionPath);
    }

    // Remove from sessions list
    const sessions = this.getSessionsList();
    const updatedSessions = sessions.filter(s => s.id !== sessionId);
    this.config.set('sessions', updatedSessions);

    if (this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }

    logger.info('Session deleted', { sessionId });
  }

  public getCurrentSession(): Session | null {
    return this.currentSession;
  }

  public getSessionsList(): Array<{
    id: string;
    name: string;
    created: Date;
    lastAccessed: Date;
    workingDirectory: string;
  }> {
    const sessions = this.config.get('sessions', []) as any[];
    return sessions.map(s => ({
      ...s,
      created: new Date(s.created),
      lastAccessed: new Date(s.lastAccessed),
    }));
  }

  public async listSessions(): Promise<Array<{
    id: string;
    name: string;
    created: Date;
    lastAccessed: Date;
    workingDirectory: string;
    size: number;
  }>> {
    const sessionsList = this.getSessionsList();
    const sessionsWithSize = await Promise.all(
      sessionsList.map(async (session) => {
        const sessionPath = path.join(this.sessionsDir, `${session.id}.json`);
        let size = 0;
        
        try {
          const stats = await fs.stat(sessionPath);
          size = stats.size;
        } catch {
          // Session file might not exist
        }

        return { ...session, size };
      })
    );

    return sessionsWithSize.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime());
  }

  public async updateCurrentSession(updates: Partial<AgentContext>): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    // Update context
    const updatedContext = await this.contextManager.updateContext(updates);
    this.currentSession.context = updatedContext;

    // Save if persistent
    if (this.currentSession.persistent) {
      await this.saveSession(this.currentSession);
    }
  }

  public async switchWorkingDirectory(newDirectory: string): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    logger.info('Switching working directory', { 
      from: this.currentSession.context.workingDirectory,
      to: newDirectory 
    });

    const resolvedPath = path.resolve(newDirectory);
    
    if (!await fs.pathExists(resolvedPath)) {
      throw new Error(`Directory does not exist: ${resolvedPath}`);
    }

    await this.updateCurrentSession({ workingDirectory: resolvedPath });
    
    // Update sessions list
    this.updateSessionsList(this.currentSession);
  }

  public async cleanupOldSessions(maxAge: number = 30): Promise<number> {
    logger.info('Cleaning up old sessions', { maxAgeDays: maxAge });

    const sessions = await this.listSessions();
    const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
    
    let deletedCount = 0;
    
    for (const session of sessions) {
      if (session.lastAccessed < cutoffDate) {
        await this.deleteSession(session.id);
        deletedCount++;
      }
    }

    logger.info('Session cleanup completed', { deletedCount });
    return deletedCount;
  }

  public async exportSession(sessionId: string, exportPath: string): Promise<void> {
    const session = await this.loadSession(sessionId);
    await fs.writeJson(exportPath, session, { spaces: 2 });
    logger.info('Session exported', { sessionId, exportPath });
  }

  public async importSession(importPath: string): Promise<Session> {
    const sessionData = await fs.readJson(importPath);
    
    // Generate new ID to avoid conflicts
    sessionData.id = nanoid();
    sessionData.name = `${sessionData.name} (imported)`;
    
    const session = await this.createSession(
      sessionData.name,
      sessionData.config,
      sessionData.context.workingDirectory,
      true
    );

    // Restore context data
    session.context = sessionData.context;
    session.context.sessionId = session.id;
    
    await this.saveSession(session);
    
    logger.info('Session imported', { sessionId: session.id, importPath });
    return session;
  }

  private updateSessionsList(session: Session): void {
    const sessions = this.getSessionsList();
    const existingIndex = sessions.findIndex(s => s.id === session.id);
    
    const sessionSummary = {
      id: session.id,
      name: session.name,
      created: session.created,
      lastAccessed: session.lastAccessed,
      workingDirectory: session.context.workingDirectory,
    };

    if (existingIndex >= 0) {
      sessions[existingIndex] = sessionSummary;
    } else {
      sessions.push(sessionSummary);
    }

    this.config.set('sessions', sessions);
  }

  public getSessionsDirectory(): string {
    return this.sessionsDir;
  }

  public async getSessionSize(sessionId: string): Promise<number> {
    const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
    
    try {
      const stats = await fs.stat(sessionPath);
      return stats.size;
    } catch {
      return 0;
    }
  }
}
