import { StreamingEvent, UIConfig, ExecutionPlan, TaskResult } from '@/types';
import { Ora } from 'ora';
import { EventEmitter } from 'events';
export declare class StreamingUI extends EventEmitter {
    private config;
    private spinners;
    private currentPlan;
    private startTime;
    constructor(config?: UIConfig);
    private setupEventHandlers;
    emitEvent(event: StreamingEvent): void;
    startExecution(plan: ExecutionPlan): void;
    completeExecution(results: TaskResult[]): void;
    private displayHeader;
    private displayPlanSummary;
    private handleTaskStart;
    private handleTaskProgress;
    private handleTaskComplete;
    private handleTaskError;
    private handlePlanUpdate;
    private handleOutput;
    private displayTaskOutput;
    private displayOverallProgress;
    private displayExecutionSummary;
    private clearAllSpinners;
    private getProgressBar;
    private getPriorityIcon;
    private formatDuration;
    setConfig(config: Partial<UIConfig>): void;
    getConfig(): UIConfig;
    streamLLMResponse(onChunk: (chunk: string) => void): (chunk: string) => void;
    displayThinking(message: string): Ora;
    displaySuccess(message: string): void;
    displayError(message: string): void;
    displayWarning(message: string): void;
    displayInfo(message: string): void;
    prompt(message: string): void;
    clear(): void;
    newLine(): void;
}
//# sourceMappingURL=streaming-ui.d.ts.map