import { Session, AgentContext, AgentConfig } from '@/types';
export declare class SessionManager {
    private static instance;
    private config;
    private currentSession;
    private contextManager;
    private sessionsDir;
    private constructor();
    static getInstance(): SessionManager;
    private ensureSessionsDirectory;
    createSession(name: string, agentConfig: AgentConfig, workingDirectory?: string, persistent?: boolean): Promise<Session>;
    loadSession(sessionId: string): Promise<Session>;
    saveSession(session: Session): Promise<void>;
    deleteSession(sessionId: string): Promise<void>;
    getCurrentSession(): Session | null;
    getSessionsList(): Array<{
        id: string;
        name: string;
        created: Date;
        lastAccessed: Date;
        workingDirectory: string;
    }>;
    listSessions(): Promise<Array<{
        id: string;
        name: string;
        created: Date;
        lastAccessed: Date;
        workingDirectory: string;
        size: number;
    }>>;
    updateCurrentSession(updates: Partial<AgentContext>): Promise<void>;
    switchWorkingDirectory(newDirectory: string): Promise<void>;
    cleanupOldSessions(maxAge?: number): Promise<number>;
    exportSession(sessionId: string, exportPath: string): Promise<void>;
    importSession(importPath: string): Promise<Session>;
    private updateSessionsList;
    getSessionsDirectory(): string;
    getSessionSize(sessionId: string): Promise<number>;
}
//# sourceMappingURL=session-manager.d.ts.map