import { <PERSON><PERSON><PERSON><PERSON> } from './llm-provider';
import OpenAI from 'openai';
export class OpenAIProvider extends LLMProvider {
    client;
    constructor(config) {
        super(config);
        this.client = new OpenAI({
            apiKey: this.apiKey,
            baseURL: config.baseUrl,
            timeout: this.timeout,
        });
    }
    async generateResponse(messages, tools, stream = false) {
        try {
            const params = {
                model: this.model,
                messages: this.formatMessages(messages),
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream,
            };
            if (tools && tools.length > 0) {
                params.tools = this.formatTools(tools);
                params.tool_choice = 'auto';
            }
            const response = await this.client.chat.completions.create(params);
            const choice = response.choices[0];
            return {
                content: choice.message.content || '',
                toolCalls: choice.message.tool_calls ? this.parseToolCalls(choice.message.tool_calls) : undefined,
                usage: response.usage ? {
                    promptTokens: response.usage.prompt_tokens,
                    completionTokens: response.usage.completion_tokens,
                    totalTokens: response.usage.total_tokens,
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async *generateStream(messages, tools, onChunk) {
        try {
            const params = {
                model: this.model,
                messages: this.formatMessages(messages),
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream: true,
            };
            if (tools && tools.length > 0) {
                params.tools = this.formatTools(tools);
                params.tool_choice = 'auto';
            }
            const stream = await this.client.chat.completions.create(params);
            let fullContent = '';
            let toolCalls = [];
            let usage;
            for await (const chunk of stream) {
                const choice = chunk.choices[0];
                if (choice?.delta?.content) {
                    fullContent += choice.delta.content;
                    if (onChunk)
                        onChunk(choice.delta.content);
                    yield choice.delta.content;
                }
                if (choice?.delta?.tool_calls) {
                    toolCalls.push(...choice.delta.tool_calls);
                }
                if (chunk.usage) {
                    usage = chunk.usage;
                }
            }
            return {
                content: fullContent,
                toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens,
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async validateConnection() {
        try {
            await this.client.models.list();
            return true;
        }
        catch {
            return false;
        }
    }
}
//# sourceMappingURL=openai.js.map