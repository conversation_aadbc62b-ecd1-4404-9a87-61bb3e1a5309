"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepSeekProvider = void 0;
const llm_provider_1 = require("./llm-provider");
const axios_1 = __importDefault(require("axios"));
class DeepSeekProvider extends llm_provider_1.LLMProvider {
    client;
    constructor(config) {
        super(config);
        this.client = axios_1.default.create({
            baseURL: config.baseUrl || 'https://api.deepseek.com/v1',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
            },
            timeout: this.timeout,
        });
    }
    async generateResponse(messages, tools, stream = false) {
        try {
            const payload = {
                model: this.model,
                messages: this.formatMessages(messages),
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream,
            };
            if (tools && tools.length > 0) {
                payload.tools = this.formatTools(tools);
                payload.tool_choice = 'auto';
            }
            const response = await this.client.post('/chat/completions', payload);
            const choice = response.data.choices[0];
            return {
                content: choice.message.content || '',
                toolCalls: choice.message.tool_calls ? this.parseToolCalls(choice.message.tool_calls) : undefined,
                usage: response.data.usage ? {
                    promptTokens: response.data.usage.prompt_tokens,
                    completionTokens: response.data.usage.completion_tokens,
                    totalTokens: response.data.usage.total_tokens,
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async *generateStream(messages, tools, onChunk) {
        try {
            const payload = {
                model: this.model,
                messages: this.formatMessages(messages),
                temperature: this.temperature,
                max_tokens: this.maxTokens,
                stream: true,
            };
            if (tools && tools.length > 0) {
                payload.tools = this.formatTools(tools);
                payload.tool_choice = 'auto';
            }
            const response = await this.client.post('/chat/completions', payload, {
                responseType: 'stream',
            });
            let fullContent = '';
            let toolCalls = [];
            let usage;
            const stream = response.data;
            for await (const chunk of this.parseSSEStream(stream)) {
                if (chunk.choices && chunk.choices[0]) {
                    const delta = chunk.choices[0].delta;
                    if (delta.content) {
                        fullContent += delta.content;
                        if (onChunk)
                            onChunk(delta.content);
                        yield delta.content;
                    }
                    if (delta.tool_calls) {
                        toolCalls.push(...delta.tool_calls);
                    }
                }
                if (chunk.usage) {
                    usage = chunk.usage;
                }
            }
            return {
                content: fullContent,
                toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens,
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async validateConnection() {
        try {
            const response = await this.client.get('/models');
            return response.status === 200;
        }
        catch {
            return false;
        }
    }
    async *parseSSEStream(stream) {
        let buffer = '';
        for await (const chunk of stream) {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]')
                        return;
                    try {
                        yield JSON.parse(data);
                    }
                    catch {
                        // Skip invalid JSON
                    }
                }
            }
        }
    }
}
exports.DeepSeekProvider = DeepSeekProvider;
//# sourceMappingURL=deepseek.js.map