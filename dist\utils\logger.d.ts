export declare class Logger {
    private static instance;
    private logger;
    private logDir;
    private constructor();
    static getInstance(): Logger;
    private ensureLogDirectory;
    private setupLogger;
    private colorizeLevel;
    info(message: string, meta?: any): void;
    warn(message: string, meta?: any): void;
    error(message: string, error?: Error | any): void;
    debug(message: string, meta?: any): void;
    verbose(message: string, meta?: any): void;
    task(taskId: string, message: string, meta?: any): void;
    agent(message: string, meta?: any): void;
    tool(toolName: string, message: string, meta?: any): void;
    session(sessionId: string, message: string, meta?: any): void;
    performance(operation: string, duration: number, meta?: any): void;
    stream(message: string): void;
    clearLine(): void;
    newLine(): void;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map