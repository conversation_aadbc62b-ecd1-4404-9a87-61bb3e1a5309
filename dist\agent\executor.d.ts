import { ExecutionPlan, Task, TaskResult, AgentContext } from '@/types';
import { ToolRegistry } from '@/tools/tool-registry';
export declare class TaskExecutor {
    private toolRegistry;
    private executionQueue;
    constructor(toolRegistry: ToolRegistry);
    executePlan(plan: ExecutionPlan, context: AgentContext): Promise<TaskResult[]>;
    private groupTasksByLevel;
    private executeLevel;
    private executeParallelGroup;
    private executeTask;
    private executeWithTimeout;
    private shouldRetry;
    private shouldStopExecution;
    executeTasksParallel(tasks: Task[], context: AgentContext, maxConcurrency?: number): Promise<TaskResult[]>;
    executeTasksSequential(tasks: Task[], context: AgentContext): Promise<TaskResult[]>;
    getExecutionStats(): {
        queueSize: number;
        pending: number;
        isPaused: boolean;
    };
    pauseExecution(): void;
    resumeExecution(): void;
    clearQueue(): void;
}
//# sourceMappingURL=executor.d.ts.map