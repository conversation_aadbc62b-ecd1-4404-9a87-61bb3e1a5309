"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileOperationsTool = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const fast_glob_1 = __importDefault(require("fast-glob"));
class FileOperationsTool {
    getReadFileTool() {
        return {
            name: 'read_file',
            description: 'Read the contents of a file',
            category: 'file',
            riskLevel: 'low',
            parallel: true,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file to read',
                    required: true,
                },
                {
                    name: 'encoding',
                    type: 'string',
                    description: 'File encoding (default: utf8)',
                    required: false,
                    default: 'utf8',
                },
            ],
            execute: async (params, context) => {
                try {
                    const filePath = path_1.default.resolve(context.workingDirectory, params.path);
                    const content = await fs_extra_1.default.readFile(filePath, params.encoding);
                    return {
                        success: true,
                        data: {
                            content,
                            path: filePath,
                            size: content.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to read file: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getWriteFileTool() {
        return {
            name: 'write_file',
            description: 'Write content to a file',
            category: 'file',
            riskLevel: 'medium',
            parallel: false,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file to write',
                    required: true,
                },
                {
                    name: 'content',
                    type: 'string',
                    description: 'Content to write to the file',
                    required: true,
                },
                {
                    name: 'encoding',
                    type: 'string',
                    description: 'File encoding (default: utf8)',
                    required: false,
                    default: 'utf8',
                },
                {
                    name: 'overwrite',
                    type: 'boolean',
                    description: 'Whether to overwrite existing file (default: true)',
                    required: false,
                    default: true,
                },
            ],
            execute: async (params, context) => {
                try {
                    const filePath = path_1.default.resolve(context.workingDirectory, params.path);
                    if (!params.overwrite && await fs_extra_1.default.pathExists(filePath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'File already exists and overwrite is disabled',
                        };
                    }
                    await fs_extra_1.default.ensureDir(path_1.default.dirname(filePath));
                    await fs_extra_1.default.writeFile(filePath, params.content, params.encoding);
                    return {
                        success: true,
                        data: {
                            path: filePath,
                            size: params.content.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to write file: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getCreateFileTool() {
        return {
            name: 'create_file',
            description: 'Create a new file with optional content',
            category: 'file',
            riskLevel: 'low',
            parallel: false,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file to create',
                    required: true,
                },
                {
                    name: 'content',
                    type: 'string',
                    description: 'Initial content for the file',
                    required: false,
                    default: '',
                },
            ],
            execute: async (params, context) => {
                try {
                    const filePath = path_1.default.resolve(context.workingDirectory, params.path);
                    if (await fs_extra_1.default.pathExists(filePath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'File already exists',
                        };
                    }
                    await fs_extra_1.default.ensureDir(path_1.default.dirname(filePath));
                    await fs_extra_1.default.writeFile(filePath, params.content || '');
                    return {
                        success: true,
                        data: {
                            path: filePath,
                            created: true,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to create file: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getDeleteFileTool() {
        return {
            name: 'delete_file',
            description: 'Delete a file or directory',
            category: 'file',
            riskLevel: 'high',
            parallel: false,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to the file or directory to delete',
                    required: true,
                },
                {
                    name: 'recursive',
                    type: 'boolean',
                    description: 'Whether to delete directories recursively (default: false)',
                    required: false,
                    default: false,
                },
            ],
            execute: async (params, context) => {
                try {
                    const targetPath = path_1.default.resolve(context.workingDirectory, params.path);
                    if (!await fs_extra_1.default.pathExists(targetPath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'File or directory does not exist',
                        };
                    }
                    const stats = await fs_extra_1.default.stat(targetPath);
                    if (stats.isDirectory() && !params.recursive) {
                        return {
                            success: false,
                            data: null,
                            error: 'Cannot delete directory without recursive flag',
                        };
                    }
                    await fs_extra_1.default.remove(targetPath);
                    return {
                        success: true,
                        data: {
                            path: targetPath,
                            deleted: true,
                            type: stats.isDirectory() ? 'directory' : 'file',
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to delete: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getCopyFileTool() {
        return {
            name: 'copy_file',
            description: 'Copy a file or directory',
            category: 'file',
            riskLevel: 'medium',
            parallel: false,
            parameters: [
                {
                    name: 'source',
                    type: 'string',
                    description: 'Source path',
                    required: true,
                },
                {
                    name: 'destination',
                    type: 'string',
                    description: 'Destination path',
                    required: true,
                },
                {
                    name: 'overwrite',
                    type: 'boolean',
                    description: 'Whether to overwrite existing files (default: false)',
                    required: false,
                    default: false,
                },
            ],
            execute: async (params, context) => {
                try {
                    const sourcePath = path_1.default.resolve(context.workingDirectory, params.source);
                    const destPath = path_1.default.resolve(context.workingDirectory, params.destination);
                    if (!await fs_extra_1.default.pathExists(sourcePath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'Source file or directory does not exist',
                        };
                    }
                    await fs_extra_1.default.copy(sourcePath, destPath, { overwrite: params.overwrite });
                    return {
                        success: true,
                        data: {
                            source: sourcePath,
                            destination: destPath,
                            copied: true,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to copy: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getMoveFileTool() {
        return {
            name: 'move_file',
            description: 'Move or rename a file or directory',
            category: 'file',
            riskLevel: 'medium',
            parallel: false,
            parameters: [
                {
                    name: 'source',
                    type: 'string',
                    description: 'Source path',
                    required: true,
                },
                {
                    name: 'destination',
                    type: 'string',
                    description: 'Destination path',
                    required: true,
                },
            ],
            execute: async (params, context) => {
                try {
                    const sourcePath = path_1.default.resolve(context.workingDirectory, params.source);
                    const destPath = path_1.default.resolve(context.workingDirectory, params.destination);
                    if (!await fs_extra_1.default.pathExists(sourcePath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'Source file or directory does not exist',
                        };
                    }
                    await fs_extra_1.default.move(sourcePath, destPath);
                    return {
                        success: true,
                        data: {
                            source: sourcePath,
                            destination: destPath,
                            moved: true,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to move: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getListDirectoryTool() {
        return {
            name: 'list_directory',
            description: 'List contents of a directory',
            category: 'file',
            riskLevel: 'low',
            parallel: true,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Directory path to list',
                    required: true,
                },
                {
                    name: 'recursive',
                    type: 'boolean',
                    description: 'Whether to list recursively (default: false)',
                    required: false,
                    default: false,
                },
                {
                    name: 'includeHidden',
                    type: 'boolean',
                    description: 'Whether to include hidden files (default: false)',
                    required: false,
                    default: false,
                },
            ],
            execute: async (params, context) => {
                try {
                    const dirPath = path_1.default.resolve(context.workingDirectory, params.path);
                    if (!await fs_extra_1.default.pathExists(dirPath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'Directory does not exist',
                        };
                    }
                    const stats = await fs_extra_1.default.stat(dirPath);
                    if (!stats.isDirectory()) {
                        return {
                            success: false,
                            data: null,
                            error: 'Path is not a directory',
                        };
                    }
                    const pattern = params.recursive ? '**/*' : '*';
                    const options = {
                        cwd: dirPath,
                        dot: params.includeHidden,
                        onlyFiles: false,
                        markDirectories: true,
                        stats: true,
                    };
                    const entries = await (0, fast_glob_1.default)(pattern, options);
                    const items = await Promise.all(entries.map(async (entry) => {
                        const itemPath = path_1.default.join(dirPath, entry.path);
                        const itemStats = entry.stats || await fs_extra_1.default.stat(itemPath);
                        return {
                            name: path_1.default.basename(entry.path),
                            path: entry.path,
                            fullPath: itemPath,
                            type: itemStats.isDirectory() ? 'directory' : 'file',
                            size: itemStats.size,
                            modified: itemStats.mtime,
                            permissions: itemStats.mode.toString(8),
                        };
                    }));
                    return {
                        success: true,
                        data: {
                            path: dirPath,
                            items,
                            count: items.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to list directory: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getSearchFilesTool() {
        return {
            name: 'search_files',
            description: 'Search for files by name pattern',
            category: 'file',
            riskLevel: 'low',
            parallel: true,
            parameters: [
                {
                    name: 'pattern',
                    type: 'string',
                    description: 'Search pattern (glob syntax)',
                    required: true,
                },
                {
                    name: 'directory',
                    type: 'string',
                    description: 'Directory to search in (default: current)',
                    required: false,
                    default: '.',
                },
                {
                    name: 'recursive',
                    type: 'boolean',
                    description: 'Whether to search recursively (default: true)',
                    required: false,
                    default: true,
                },
            ],
            execute: async (params, context) => {
                try {
                    const searchDir = path_1.default.resolve(context.workingDirectory, params.directory);
                    const searchPattern = params.recursive ? `**/${params.pattern}` : params.pattern;
                    const files = await (0, fast_glob_1.default)(searchPattern, {
                        cwd: searchDir,
                        onlyFiles: true,
                        absolute: true,
                    });
                    const results = await Promise.all(files.map(async (filePath) => {
                        const stats = await fs_extra_1.default.stat(filePath);
                        return {
                            path: path_1.default.relative(context.workingDirectory, filePath),
                            fullPath: filePath,
                            name: path_1.default.basename(filePath),
                            size: stats.size,
                            modified: stats.mtime,
                        };
                    }));
                    return {
                        success: true,
                        data: {
                            pattern: params.pattern,
                            directory: searchDir,
                            results,
                            count: results.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to search files: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getGrepTool() {
        return {
            name: 'grep',
            description: 'Search for text patterns within files',
            category: 'file',
            riskLevel: 'low',
            parallel: true,
            parameters: [
                {
                    name: 'pattern',
                    type: 'string',
                    description: 'Text pattern to search for (regex supported)',
                    required: true,
                },
                {
                    name: 'files',
                    type: 'array',
                    description: 'Files to search in',
                    required: true,
                },
                {
                    name: 'caseSensitive',
                    type: 'boolean',
                    description: 'Whether search is case sensitive (default: false)',
                    required: false,
                    default: false,
                },
                {
                    name: 'lineNumbers',
                    type: 'boolean',
                    description: 'Include line numbers in results (default: true)',
                    required: false,
                    default: true,
                },
            ],
            execute: async (params, context) => {
                try {
                    const regex = new RegExp(params.pattern, params.caseSensitive ? 'g' : 'gi');
                    const results = [];
                    for (const filePath of params.files) {
                        const fullPath = path_1.default.resolve(context.workingDirectory, filePath);
                        if (!await fs_extra_1.default.pathExists(fullPath)) {
                            continue;
                        }
                        const content = await fs_extra_1.default.readFile(fullPath, 'utf8');
                        const lines = content.split('\n');
                        const matches = [];
                        lines.forEach((line, index) => {
                            if (regex.test(line)) {
                                matches.push({
                                    line: params.lineNumbers ? index + 1 : undefined,
                                    content: line.trim(),
                                    match: line.match(regex)?.[0],
                                });
                            }
                        });
                        if (matches.length > 0) {
                            results.push({
                                file: filePath,
                                fullPath,
                                matches,
                                matchCount: matches.length,
                            });
                        }
                    }
                    return {
                        success: true,
                        data: {
                            pattern: params.pattern,
                            results,
                            totalMatches: results.reduce((sum, r) => sum + r.matchCount, 0),
                            filesWithMatches: results.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to grep: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getGlobTool() {
        return {
            name: 'glob',
            description: 'Find files using glob patterns',
            category: 'file',
            riskLevel: 'low',
            parallel: true,
            parameters: [
                {
                    name: 'patterns',
                    type: 'array',
                    description: 'Glob patterns to match',
                    required: true,
                },
                {
                    name: 'directory',
                    type: 'string',
                    description: 'Base directory for search (default: current)',
                    required: false,
                    default: '.',
                },
                {
                    name: 'ignore',
                    type: 'array',
                    description: 'Patterns to ignore',
                    required: false,
                    default: [],
                },
            ],
            execute: async (params, context) => {
                try {
                    const baseDir = path_1.default.resolve(context.workingDirectory, params.directory);
                    const files = await (0, fast_glob_1.default)(params.patterns, {
                        cwd: baseDir,
                        ignore: params.ignore,
                        absolute: true,
                    });
                    const results = await Promise.all(files.map(async (filePath) => {
                        const stats = await fs_extra_1.default.stat(filePath);
                        return {
                            path: path_1.default.relative(context.workingDirectory, filePath),
                            fullPath: filePath,
                            name: path_1.default.basename(filePath),
                            type: stats.isDirectory() ? 'directory' : 'file',
                            size: stats.size,
                            modified: stats.mtime,
                        };
                    }));
                    return {
                        success: true,
                        data: {
                            patterns: params.patterns,
                            directory: baseDir,
                            results,
                            count: results.length,
                        },
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to glob: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
    getPermissionsTool() {
        return {
            name: 'permissions',
            description: 'Get or set file/directory permissions',
            category: 'file',
            riskLevel: 'medium',
            parallel: false,
            parameters: [
                {
                    name: 'path',
                    type: 'string',
                    description: 'Path to file or directory',
                    required: true,
                },
                {
                    name: 'mode',
                    type: 'string',
                    description: 'Permission mode to set (e.g., "755", "644")',
                    required: false,
                },
                {
                    name: 'recursive',
                    type: 'boolean',
                    description: 'Apply recursively to directories (default: false)',
                    required: false,
                    default: false,
                },
            ],
            execute: async (params, context) => {
                try {
                    const targetPath = path_1.default.resolve(context.workingDirectory, params.path);
                    if (!await fs_extra_1.default.pathExists(targetPath)) {
                        return {
                            success: false,
                            data: null,
                            error: 'File or directory does not exist',
                        };
                    }
                    const stats = await fs_extra_1.default.stat(targetPath);
                    const currentMode = stats.mode.toString(8).slice(-3);
                    if (params.mode) {
                        // Set permissions
                        const newMode = parseInt(params.mode, 8);
                        if (params.recursive && stats.isDirectory()) {
                            const files = await (0, fast_glob_1.default)('**/*', { cwd: targetPath, absolute: true });
                            await Promise.all([
                                fs_extra_1.default.chmod(targetPath, newMode),
                                ...files.map(file => fs_extra_1.default.chmod(file, newMode))
                            ]);
                        }
                        else {
                            await fs_extra_1.default.chmod(targetPath, newMode);
                        }
                        return {
                            success: true,
                            data: {
                                path: targetPath,
                                previousMode: currentMode,
                                newMode: params.mode,
                                recursive: params.recursive,
                            },
                        };
                    }
                    else {
                        // Get permissions
                        return {
                            success: true,
                            data: {
                                path: targetPath,
                                mode: currentMode,
                                type: stats.isDirectory() ? 'directory' : 'file',
                                readable: !!(stats.mode & parseInt('400', 8)),
                                writable: !!(stats.mode & parseInt('200', 8)),
                                executable: !!(stats.mode & parseInt('100', 8)),
                            },
                        };
                    }
                }
                catch (error) {
                    return {
                        success: false,
                        data: null,
                        error: `Failed to handle permissions: ${error instanceof Error ? error.message : String(error)}`,
                    };
                }
            },
        };
    }
}
exports.FileOperationsTool = FileOperationsTool;
//# sourceMappingURL=file-operations.js.map