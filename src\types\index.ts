import { z } from 'zod';

// Core Agent Types
export interface AgentConfig {
  provider: LLMProvider;
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface AgentContext {
  sessionId: string;
  workingDirectory: string;
  projectStructure: ProjectStructure;
  environment: EnvironmentInfo;
  history: ExecutionHistory[];
  memory: ContextMemory;
}

export interface ExecutionPlan {
  id: string;
  tasks: Task[];
  dependencies: TaskDependency[];
  parallelGroups: ParallelGroup[];
  estimatedDuration: number;
}

export interface Task {
  id: string;
  type: TaskType;
  description: string;
  tool: string;
  parameters: Record<string, any>;
  priority: number;
  retryCount: number;
  maxRetries: number;
  timeout: number;
  dependencies: string[];
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  output: any;
  error?: Error;
  duration: number;
  timestamp: Date;
}

export interface ParallelGroup {
  id: string;
  taskIds: string[];
  maxConcurrency: number;
}

export interface TaskDependency {
  taskId: string;
  dependsOn: string[];
}

// LLM Provider Types
export type LLMProvider = 'deepseek' | 'openai' | 'ollama' | 'anthropic' | 'gemini' | 'mistral';

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[] | undefined;
  toolCallId?: string | undefined;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface LLMResponse {
  content: string;
  toolCalls?: ToolCall[] | undefined;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  } | undefined;
}

// Tool System Types
export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameter[];
  execute: (params: Record<string, any>, context: AgentContext) => Promise<ToolResult>;
  category: ToolCategory;
  riskLevel: RiskLevel;
  parallel: boolean;
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  default?: any;
  enum?: string[];
}

export interface ToolResult {
  success: boolean;
  data: any;
  error?: string;
  metadata?: Record<string, any>;
}

export type ToolCategory = 'file' | 'shell' | 'network' | 'system' | 'analysis' | 'generation';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type TaskType = 'file_operation' | 'shell_command' | 'analysis' | 'generation' | 'network';

// Context and Session Types
export interface ProjectStructure {
  root: string;
  files: FileInfo[];
  directories: DirectoryInfo[];
  gitInfo?: GitInfo | undefined;
  packageInfo?: PackageInfo | undefined;
  dependencies: string[];
}

export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  lastModified: Date;
  type: 'file' | 'directory' | 'symlink';
  permissions: string;
}

export interface DirectoryInfo {
  path: string;
  name: string;
  children: string[];
  size: number;
}

export interface GitInfo {
  branch: string;
  commit: string;
  remote?: string | undefined;
  status: string[];
}

export interface PackageInfo {
  name: string;
  version: string;
  type: 'npm' | 'python' | 'rust' | 'go' | 'other';
  dependencies: Record<string, string>;
  scripts: Record<string, string>;
}

export interface EnvironmentInfo {
  platform: string;
  arch: string;
  nodeVersion?: string;
  pythonVersion?: string;
  shell: string;
  env: Record<string, string>;
}

export interface ExecutionHistory {
  id: string;
  timestamp: Date;
  command: string;
  result: TaskResult;
  context: Partial<AgentContext>;
}

export interface ContextMemory {
  shortTerm: MemoryEntry[];
  longTerm: MemoryEntry[];
  patterns: PatternMemory[];
}

export interface MemoryEntry {
  id: string;
  content: string;
  type: 'fact' | 'pattern' | 'preference' | 'error';
  importance: number;
  timestamp: Date;
  tags: string[];
}

export interface PatternMemory {
  pattern: string;
  frequency: number;
  success: boolean;
  lastUsed: Date;
}

// Session Types
export interface Session {
  id: string;
  name: string;
  created: Date;
  lastAccessed: Date;
  context: AgentContext;
  config: AgentConfig;
  persistent: boolean;
}

// UI Types
export interface StreamingEvent {
  type: 'task_start' | 'task_progress' | 'task_complete' | 'task_error' | 'plan_update' | 'output';
  data: any;
  timestamp: Date;
}

export interface UIConfig {
  theme: 'dark' | 'light' | 'auto';
  verbose: boolean;
  showProgress: boolean;
  showTimestamps: boolean;
  maxOutputLines: number;
}

// Validation Schemas
export const AgentConfigSchema = z.object({
  provider: z.enum(['deepseek', 'openai', 'ollama', 'anthropic', 'gemini', 'mistral']),
  model: z.string(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
  timeout: z.number().positive().optional(),
});

export const TaskSchema = z.object({
  id: z.string(),
  type: z.enum(['file_operation', 'shell_command', 'analysis', 'generation', 'network']),
  description: z.string(),
  tool: z.string(),
  parameters: z.record(z.any()),
  priority: z.number().min(1).max(10),
  retryCount: z.number().min(0).default(0),
  maxRetries: z.number().min(0).default(3),
  timeout: z.number().positive().default(30000),
  dependencies: z.array(z.string()).default([]),
});
