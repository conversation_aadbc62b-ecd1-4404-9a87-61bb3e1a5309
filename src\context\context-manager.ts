import { AgentContext, ProjectStructure, EnvironmentInfo, ContextMemory, MemoryEntry, PatternMemory } from '@/types';
import { ProjectIndexer } from './project-indexer';
import { logger } from '@/utils/logger';
import { nanoid } from 'nanoid';
import path from 'path';
import os from 'os';

export class ContextManager {
  private static instance: ContextManager;
  private projectIndexer: ProjectIndexer;
  private currentContext: AgentContext | null = null;

  private constructor() {
    this.projectIndexer = new ProjectIndexer();
  }

  public static getInstance(): ContextManager {
    if (!ContextManager.instance) {
      ContextManager.instance = new ContextManager();
    }
    return ContextManager.instance;
  }

  public async initializeContext(
    sessionId: string,
    workingDirectory: string = process.cwd()
  ): Promise<AgentContext> {
    logger.info('Initializing context', { sessionId, workingDirectory });

    const projectStructure = await this.projectIndexer.indexProject(workingDirectory);
    const environmentInfo = this.getEnvironmentInfo();
    const memory = this.initializeMemory();

    this.currentContext = {
      sessionId,
      workingDirectory: path.resolve(workingDirectory),
      projectStructure,
      environment: environmentInfo,
      history: [],
      memory,
    };

    logger.info('Context initialized', {
      sessionId,
      projectRoot: projectStructure.root,
      fileCount: projectStructure.files.length,
      directoryCount: projectStructure.directories.length,
    });

    return this.currentContext;
  }

  public getCurrentContext(): AgentContext | null {
    return this.currentContext;
  }

  public async updateContext(updates: Partial<AgentContext>): Promise<AgentContext> {
    if (!this.currentContext) {
      throw new Error('No context initialized');
    }

    this.currentContext = {
      ...this.currentContext,
      ...updates,
    };

    // Re-index project if working directory changed
    if (updates.workingDirectory && updates.workingDirectory !== this.currentContext.workingDirectory) {
      this.currentContext.projectStructure = await this.projectIndexer.indexProject(updates.workingDirectory);
    }

    return this.currentContext;
  }

  public async refreshProjectStructure(): Promise<ProjectStructure> {
    if (!this.currentContext) {
      throw new Error('No context initialized');
    }

    const newStructure = await this.projectIndexer.indexProject(this.currentContext.workingDirectory);
    this.currentContext.projectStructure = newStructure;

    logger.debug('Project structure refreshed', {
      fileCount: newStructure.files.length,
      directoryCount: newStructure.directories.length,
    });

    return newStructure;
  }

  public addMemory(content: string, type: MemoryEntry['type'], importance: number = 5, tags: string[] = []): void {
    if (!this.currentContext) {
      return;
    }

    const memory: MemoryEntry = {
      id: nanoid(),
      content,
      type,
      importance,
      timestamp: new Date(),
      tags,
    };

    // Add to short-term memory
    this.currentContext.memory.shortTerm.push(memory);

    // Move to long-term if important enough
    if (importance >= 8) {
      this.currentContext.memory.longTerm.push(memory);
    }

    // Limit short-term memory size
    if (this.currentContext.memory.shortTerm.length > 100) {
      this.currentContext.memory.shortTerm = this.currentContext.memory.shortTerm
        .sort((a, b) => b.importance - a.importance)
        .slice(0, 50);
    }

    logger.debug('Memory added', { type, importance, tags });
  }

  public addPattern(pattern: string, success: boolean): void {
    if (!this.currentContext) {
      return;
    }

    const existingPattern = this.currentContext.memory.patterns.find(p => p.pattern === pattern);
    
    if (existingPattern) {
      existingPattern.frequency++;
      existingPattern.success = success;
      existingPattern.lastUsed = new Date();
    } else {
      const patternMemory: PatternMemory = {
        pattern,
        frequency: 1,
        success,
        lastUsed: new Date(),
      };
      this.currentContext.memory.patterns.push(patternMemory);
    }

    logger.debug('Pattern recorded', { pattern, success });
  }

  public getRelevantMemories(query: string, limit: number = 10): MemoryEntry[] {
    if (!this.currentContext) {
      return [];
    }

    const allMemories = [
      ...this.currentContext.memory.shortTerm,
      ...this.currentContext.memory.longTerm,
    ];

    // Simple relevance scoring based on content similarity and importance
    const scoredMemories = allMemories.map(memory => {
      let score = memory.importance;
      
      // Boost score if query terms appear in content
      const queryTerms = query.toLowerCase().split(/\s+/);
      const contentLower = memory.content.toLowerCase();
      
      queryTerms.forEach(term => {
        if (contentLower.includes(term)) {
          score += 3;
        }
      });

      // Boost score for recent memories
      const daysSinceCreated = (Date.now() - memory.timestamp.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreated < 1) score += 2;
      else if (daysSinceCreated < 7) score += 1;

      return { memory, score };
    });

    return scoredMemories
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.memory);
  }

  public getSuccessfulPatterns(): PatternMemory[] {
    if (!this.currentContext) {
      return [];
    }

    return this.currentContext.memory.patterns
      .filter(p => p.success && p.frequency > 1)
      .sort((a, b) => b.frequency - a.frequency);
  }

  public getContextSummary(): string {
    if (!this.currentContext) {
      return 'No context available';
    }

    const { projectStructure, environment, memory } = this.currentContext;
    
    const summary = [
      `Working Directory: ${this.currentContext.workingDirectory}`,
      `Project Type: ${projectStructure.packageInfo?.type || 'Unknown'}`,
      `Files: ${projectStructure.files.length}`,
      `Directories: ${projectStructure.directories.length}`,
      `Platform: ${environment.platform} (${environment.arch})`,
      `Shell: ${environment.shell}`,
      `Short-term Memories: ${memory.shortTerm.length}`,
      `Long-term Memories: ${memory.longTerm.length}`,
      `Patterns: ${memory.patterns.length}`,
    ];

    if (projectStructure.gitInfo) {
      summary.push(`Git Branch: ${projectStructure.gitInfo.branch}`);
      summary.push(`Git Status: ${projectStructure.gitInfo.status.length} changes`);
    }

    if (projectStructure.packageInfo) {
      summary.push(`Package: ${projectStructure.packageInfo.name}@${projectStructure.packageInfo.version}`);
      summary.push(`Dependencies: ${Object.keys(projectStructure.packageInfo.dependencies).length}`);
    }

    return summary.join('\n');
  }

  private getEnvironmentInfo(): EnvironmentInfo {
    const env: Record<string, string> = {};
    for (const [key, value] of Object.entries(process.env)) {
      if (value !== undefined) {
        env[key] = value;
      }
    }

    return {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      shell: process.env.SHELL || process.env.ComSpec || 'unknown',
      env,
    };
  }

  private initializeMemory(): ContextMemory {
    return {
      shortTerm: [],
      longTerm: [],
      patterns: [],
    };
  }

  public async analyzeProjectChanges(): Promise<{
    newFiles: string[];
    modifiedFiles: string[];
    deletedFiles: string[];
  }> {
    if (!this.currentContext) {
      throw new Error('No context initialized');
    }

    const oldStructure = this.currentContext.projectStructure;
    const newStructure = await this.projectIndexer.indexProject(this.currentContext.workingDirectory);

    const oldFilePaths = new Set(oldStructure.files.map(f => f.path));
    const newFilePaths = new Set(newStructure.files.map(f => f.path));

    const newFiles = newStructure.files
      .filter(f => !oldFilePaths.has(f.path))
      .map(f => f.path);

    const deletedFiles = oldStructure.files
      .filter(f => !newFilePaths.has(f.path))
      .map(f => f.path);

    const modifiedFiles = newStructure.files
      .filter(newFile => {
        const oldFile = oldStructure.files.find(f => f.path === newFile.path);
        return oldFile && oldFile.lastModified.getTime() !== newFile.lastModified.getTime();
      })
      .map(f => f.path);

    // Update context with new structure
    this.currentContext.projectStructure = newStructure;

    return { newFiles, modifiedFiles, deletedFiles };
  }

  public getWorkingDirectory(): string {
    return this.currentContext?.workingDirectory || process.cwd();
  }

  public getProjectType(): string {
    return this.currentContext?.projectStructure.packageInfo?.type || 'unknown';
  }

  public hasGitRepository(): boolean {
    return !!this.currentContext?.projectStructure.gitInfo;
  }

  public getDependencies(): string[] {
    return this.currentContext?.projectStructure.dependencies || [];
  }
}
