import winston from 'winston';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs-extra';
export class Logger {
    static instance;
    logger;
    logDir;
    constructor() {
        this.logDir = path.join(process.cwd(), '.ai-cli', 'logs');
        this.ensureLogDirectory();
        this.setupLogger();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    ensureLogDirectory() {
        fs.ensureDirSync(this.logDir);
    }
    setupLogger() {
        const logFormat = winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json());
        const consoleFormat = winston.format.combine(winston.format.timestamp({ format: 'HH:mm:ss' }), winston.format.printf(({ level, message, timestamp, ...meta }) => {
            const coloredLevel = this.colorizeLevel(level);
            const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
            return `${chalk.gray(timestamp)} ${coloredLevel} ${message}${metaStr}`;
        }));
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: logFormat,
            transports: [
                new winston.transports.File({
                    filename: path.join(this.logDir, 'error.log'),
                    level: 'error',
                    maxsize: 5242880, // 5MB
                    maxFiles: 5,
                }),
                new winston.transports.File({
                    filename: path.join(this.logDir, 'combined.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 5,
                }),
            ],
        });
        if (process.env.NODE_ENV !== 'production') {
            this.logger.add(new winston.transports.Console({
                format: consoleFormat,
            }));
        }
    }
    colorizeLevel(level) {
        switch (level) {
            case 'error':
                return chalk.red('ERROR');
            case 'warn':
                return chalk.yellow('WARN ');
            case 'info':
                return chalk.blue('INFO ');
            case 'debug':
                return chalk.green('DEBUG');
            default:
                return level.toUpperCase();
        }
    }
    info(message, meta) {
        this.logger.info(message, meta);
    }
    warn(message, meta) {
        this.logger.warn(message, meta);
    }
    error(message, error) {
        this.logger.error(message, error);
    }
    debug(message, meta) {
        this.logger.debug(message, meta);
    }
    verbose(message, meta) {
        this.logger.verbose(message, meta);
    }
    task(taskId, message, meta) {
        this.info(`[Task:${taskId}] ${message}`, meta);
    }
    agent(message, meta) {
        this.info(`[Agent] ${message}`, meta);
    }
    tool(toolName, message, meta) {
        this.info(`[Tool:${toolName}] ${message}`, meta);
    }
    session(sessionId, message, meta) {
        this.info(`[Session:${sessionId}] ${message}`, meta);
    }
    performance(operation, duration, meta) {
        const color = duration > 5000 ? chalk.red : duration > 1000 ? chalk.yellow : chalk.green;
        this.info(`[Performance] ${operation} completed in ${color(`${duration}ms`)}`, meta);
    }
    stream(message) {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.write(message);
        }
    }
    clearLine() {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.clearLine(0);
            process.stdout.cursorTo(0);
        }
    }
    newLine() {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.write('\n');
        }
    }
}
export const logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map