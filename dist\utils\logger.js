"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
const winston_1 = __importDefault(require("winston"));
const chalk_1 = __importDefault(require("chalk"));
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
class Logger {
    static instance;
    logger;
    logDir;
    constructor() {
        this.logDir = path_1.default.join(process.cwd(), '.ai-cli', 'logs');
        this.ensureLogDirectory();
        this.setupLogger();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    ensureLogDirectory() {
        fs_extra_1.default.ensureDirSync(this.logDir);
    }
    setupLogger() {
        const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
        const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.printf(({ level, message, timestamp, ...meta }) => {
            const coloredLevel = this.colorizeLevel(level);
            const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
            return `${chalk_1.default.gray(timestamp)} ${coloredLevel} ${message}${metaStr}`;
        }));
        this.logger = winston_1.default.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: logFormat,
            transports: [
                new winston_1.default.transports.File({
                    filename: path_1.default.join(this.logDir, 'error.log'),
                    level: 'error',
                    maxsize: 5242880, // 5MB
                    maxFiles: 5,
                }),
                new winston_1.default.transports.File({
                    filename: path_1.default.join(this.logDir, 'combined.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 5,
                }),
            ],
        });
        if (process.env.NODE_ENV !== 'production') {
            this.logger.add(new winston_1.default.transports.Console({
                format: consoleFormat,
            }));
        }
    }
    colorizeLevel(level) {
        switch (level) {
            case 'error':
                return chalk_1.default.red('ERROR');
            case 'warn':
                return chalk_1.default.yellow('WARN ');
            case 'info':
                return chalk_1.default.blue('INFO ');
            case 'debug':
                return chalk_1.default.green('DEBUG');
            default:
                return level.toUpperCase();
        }
    }
    info(message, meta) {
        this.logger.info(message, meta);
    }
    warn(message, meta) {
        this.logger.warn(message, meta);
    }
    error(message, error) {
        this.logger.error(message, error);
    }
    debug(message, meta) {
        this.logger.debug(message, meta);
    }
    verbose(message, meta) {
        this.logger.verbose(message, meta);
    }
    task(taskId, message, meta) {
        this.info(`[Task:${taskId}] ${message}`, meta);
    }
    agent(message, meta) {
        this.info(`[Agent] ${message}`, meta);
    }
    tool(toolName, message, meta) {
        this.info(`[Tool:${toolName}] ${message}`, meta);
    }
    session(sessionId, message, meta) {
        this.info(`[Session:${sessionId}] ${message}`, meta);
    }
    performance(operation, duration, meta) {
        const color = duration > 5000 ? chalk_1.default.red : duration > 1000 ? chalk_1.default.yellow : chalk_1.default.green;
        this.info(`[Performance] ${operation} completed in ${color(`${duration}ms`)}`, meta);
    }
    stream(message) {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.write(message);
        }
    }
    clearLine() {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.clearLine(0);
            process.stdout.cursorTo(0);
        }
    }
    newLine() {
        if (process.env.NODE_ENV !== 'production') {
            process.stdout.write('\n');
        }
    }
}
exports.Logger = Logger;
exports.logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map