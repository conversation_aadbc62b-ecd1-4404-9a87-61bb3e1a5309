import { LLMProvider, LLMProviderConfig } from './llm-provider';
import { LLMProvider as LLMProviderType } from '@/types';
export declare class ProviderFactory {
    static createProvider(type: LLMProviderType, config: LLMProviderConfig): LLMProvider;
    static getSupportedProviders(): LLMProviderType[];
    static getProviderModels(provider: LLMProviderType): string[];
    static getDefaultModel(provider: LLMProviderType): string;
    static validateProviderConfig(provider: LLMProviderType, config: LLMProviderConfig): boolean;
}
export * from './llm-provider';
export * from './deepseek';
export * from './openai';
export * from './anthropic';
//# sourceMappingURL=index.d.ts.map