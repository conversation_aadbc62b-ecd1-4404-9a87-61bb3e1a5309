import { LLMMessage, LLMResponse, ToolCall } from '@/types';

export abstract class LL<PERSON><PERSON>ider {
  protected apiKey: string;
  protected model: string;
  protected temperature: number;
  protected maxTokens: number;
  protected timeout: number;

  constructor(config: {
    apiKey: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
  }) {
    this.apiKey = config.apiKey;
    this.model = config.model;
    this.temperature = config.temperature ?? 0.7;
    this.maxTokens = config.maxTokens ?? 4096;
    this.timeout = config.timeout ?? 30000;
  }

  abstract generateResponse(
    messages: LLMMessage[],
    tools?: any[],
    stream?: boolean
  ): Promise<LLMResponse>;

  abstract generateStream(
    messages: LLMMessage[],
    tools?: any[],
    onChunk?: (chunk: string) => void
  ): AsyncGenerator<string, LLMResponse, unknown>;

  abstract validateConnection(): Promise<boolean>;

  protected formatMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.toolCalls && { tool_calls: msg.toolCalls }),
      ...(msg.toolCallId && { tool_call_id: msg.toolCallId })
    }));
  }

  protected formatTools(tools: any[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object',
          properties: tool.parameters.reduce((acc: any, param: any) => {
            acc[param.name] = {
              type: param.type,
              description: param.description,
              ...(param.enum && { enum: param.enum })
            };
            return acc;
          }, {}),
          required: tool.parameters.filter((p: any) => p.required).map((p: any) => p.name)
        }
      }
    }));
  }

  protected parseToolCalls(toolCalls: any[]): ToolCall[] {
    return toolCalls.map(call => ({
      id: call.id,
      type: 'function',
      function: {
        name: call.function.name,
        arguments: call.function.arguments
      }
    }));
  }

  protected handleError(error: any): never {
    if (error.response) {
      throw new Error(`LLM API Error: ${error.response.status} - ${error.response.data?.error?.message || error.message}`);
    } else if (error.request) {
      throw new Error('LLM API Error: No response received');
    } else {
      throw new Error(`LLM Error: ${error.message}`);
    }
  }

  public getModel(): string {
    return this.model;
  }

  public getProvider(): string {
    return this.constructor.name.replace('Provider', '').toLowerCase();
  }
}

export interface LLMProviderConfig {
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  baseUrl?: string;
}
