import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Context, ToolCategory, RiskLevel } from '@/types';
import { logger } from '@/utils/logger';
import { FileOperationsTool } from './file-operations';
import { ShellCommandsTool } from './shell-commands';

export class ToolRegistry {
  private static instance: ToolRegistry;
  private tools: Map<string, Tool> = new Map();
  private categories: Map<ToolCategory, string[]> = new Map();

  private constructor() {
    this.registerBuiltinTools();
  }

  public static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  private registerBuiltinTools(): void {
    // Register file operations tools
    const fileOps = new FileOperationsTool();
    this.registerTool(fileOps.getReadFileTool());
    this.registerTool(fileOps.getWriteFileTool());
    this.registerTool(fileOps.getCreateFileTool());
    this.registerTool(fileOps.getDeleteFileTool());
    this.registerTool(fileOps.getCopyFileTool());
    this.registerTool(fileOps.getMoveFileTool());
    this.registerTool(fileOps.getListDirectoryTool());
    this.registerTool(fileOps.getSearchFilesTool());
    this.registerTool(fileOps.getGrepTool());
    this.registerTool(fileOps.getGlobTool());
    this.registerTool(fileOps.getPermissionsTool());

    // Register shell commands tools
    const shellOps = new ShellCommandsTool();
    this.registerTool(shellOps.getExecuteCommandTool());
    this.registerTool(shellOps.getExecuteScriptTool());
    this.registerTool(shellOps.getProcessManagementTool());
    this.registerTool(shellOps.getEnvironmentTool());
  }

  public registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    
    // Add to category index
    if (!this.categories.has(tool.category)) {
      this.categories.set(tool.category, []);
    }
    this.categories.get(tool.category)!.push(tool.name);
    
    logger.debug(`Registered tool: ${tool.name}`, { category: tool.category, riskLevel: tool.riskLevel });
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolsByCategory(category: ToolCategory): Tool[] {
    const toolNames = this.categories.get(category) || [];
    return toolNames.map(name => this.tools.get(name)!).filter(Boolean);
  }

  public getToolsByRiskLevel(riskLevel: RiskLevel): Tool[] {
    return this.getAllTools().filter(tool => tool.riskLevel === riskLevel);
  }

  public getParallelTools(): Tool[] {
    return this.getAllTools().filter(tool => tool.parallel);
  }

  public async executeTool(
    toolName: string,
    parameters: Record<string, any>,
    context: AgentContext
  ): Promise<ToolResult> {
    const tool = this.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    logger.tool(toolName, 'Executing tool', { parameters, riskLevel: tool.riskLevel });
    
    const startTime = Date.now();
    try {
      // Validate parameters
      this.validateParameters(tool, parameters);
      
      // Execute tool
      const result = await tool.execute(parameters, context);
      
      const duration = Date.now() - startTime;
      logger.performance(`Tool:${toolName}`, duration);
      logger.tool(toolName, 'Tool execution completed', { success: result.success, duration });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Tool execution failed: ${toolName}`, error);
      
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : String(error),
        metadata: { duration, toolName }
      };
    }
  }

  public async executeToolsParallel(
    executions: Array<{
      toolName: string;
      parameters: Record<string, any>;
    }>,
    context: AgentContext,
    maxConcurrency: number = 5
  ): Promise<ToolResult[]> {
    const pLimit = (await import('p-limit')).default;
    const limit = pLimit(maxConcurrency);
    
    const promises = executions.map(({ toolName, parameters }) =>
      limit(() => this.executeTool(toolName, parameters, context))
    );
    
    return Promise.all(promises);
  }

  private validateParameters(tool: Tool, parameters: Record<string, any>): void {
    for (const param of tool.parameters) {
      if (param.required && !(param.name in parameters)) {
        throw new Error(`Missing required parameter: ${param.name}`);
      }
      
      if (param.name in parameters) {
        const value = parameters[param.name];
        
        // Type validation
        if (!this.validateParameterType(value, param.type)) {
          throw new Error(`Invalid type for parameter ${param.name}: expected ${param.type}`);
        }
        
        // Enum validation
        if (param.enum && !param.enum.includes(value)) {
          throw new Error(`Invalid value for parameter ${param.name}: must be one of ${param.enum.join(', ')}`);
        }
      } else if (param.default !== undefined) {
        parameters[param.name] = param.default;
      }
    }
  }

  private validateParameterType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  public getToolsForLLM(): any[] {
    return this.getAllTools().map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }));
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasRiskyTools(toolNames: string[]): boolean {
    return toolNames.some(name => {
      const tool = this.getTool(name);
      return tool && (tool.riskLevel === 'high' || tool.riskLevel === 'critical');
    });
  }
}
