"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMProvider = void 0;
class LLMProvider {
    apiKey;
    model;
    temperature;
    maxTokens;
    timeout;
    constructor(config) {
        this.apiKey = config.apiKey;
        this.model = config.model;
        this.temperature = config.temperature ?? 0.7;
        this.maxTokens = config.maxTokens ?? 4096;
        this.timeout = config.timeout ?? 30000;
    }
    formatMessages(messages) {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.toolCalls && { tool_calls: msg.toolCalls }),
            ...(msg.toolCallId && { tool_call_id: msg.toolCallId })
        }));
    }
    formatTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: {
                    type: 'object',
                    properties: tool.parameters.reduce((acc, param) => {
                        acc[param.name] = {
                            type: param.type,
                            description: param.description,
                            ...(param.enum && { enum: param.enum })
                        };
                        return acc;
                    }, {}),
                    required: tool.parameters.filter((p) => p.required).map((p) => p.name)
                }
            }
        }));
    }
    parseToolCalls(toolCalls) {
        return toolCalls.map(call => ({
            id: call.id,
            type: 'function',
            function: {
                name: call.function.name,
                arguments: call.function.arguments
            }
        }));
    }
    handleError(error) {
        if (error.response) {
            throw new Error(`LLM API Error: ${error.response.status} - ${error.response.data?.error?.message || error.message}`);
        }
        else if (error.request) {
            throw new Error('LLM API Error: No response received');
        }
        else {
            throw new Error(`LLM Error: ${error.message}`);
        }
    }
    getModel() {
        return this.model;
    }
    getProvider() {
        return this.constructor.name.replace('Provider', '').toLowerCase();
    }
}
exports.LLMProvider = LLMProvider;
//# sourceMappingURL=llm-provider.js.map