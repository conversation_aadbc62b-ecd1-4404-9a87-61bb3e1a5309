import { ProjectStructure } from '@/types';
export declare class ProjectIndexer {
    private readonly ignoredPatterns;
    indexProject(rootPath: string): Promise<ProjectStructure>;
    private indexFiles;
    private indexDirectories;
    private getGitInfo;
    private getPackageInfo;
    private parsePackageJson;
    private parsePyprojectToml;
    private parseRequirementsTxt;
    private parseCargoToml;
    private parseGoMod;
    private getDependencies;
}
//# sourceMappingURL=project-indexer.d.ts.map