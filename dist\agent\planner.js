import { logger } from '@/utils/logger';
import { nanoid } from 'nanoid';
export class TaskPlanner {
    toolRegistry;
    constructor(toolRegistry) {
        this.toolRegistry = toolRegistry;
    }
    async createExecutionPlan(toolCalls, context) {
        logger.debug('Creating execution plan', { toolCallCount: toolCalls.length });
        const tasks = await this.convertToolCallsToTasks(toolCalls);
        const dependencies = this.analyzeDependencies(tasks);
        const parallelGroups = this.identifyParallelGroups(tasks, dependencies);
        const estimatedDuration = this.estimateExecutionTime(tasks, parallelGroups);
        const plan = {
            id: nanoid(),
            tasks,
            dependencies,
            parallelGroups,
            estimatedDuration,
        };
        logger.debug('Execution plan created', {
            planId: plan.id,
            taskCount: tasks.length,
            dependencyCount: dependencies.length,
            parallelGroupCount: parallelGroups.length,
            estimatedDuration,
        });
        return plan;
    }
    async convertToolCallsToTasks(toolCalls) {
        const tasks = [];
        for (const toolCall of toolCalls) {
            const tool = this.toolRegistry.getTool(toolCall.function.name);
            if (!tool) {
                logger.warn(`Unknown tool: ${toolCall.function.name}`);
                continue;
            }
            let parameters;
            try {
                parameters = JSON.parse(toolCall.function.arguments);
            }
            catch (error) {
                logger.error(`Invalid tool arguments for ${toolCall.function.name}`, error);
                continue;
            }
            const task = {
                id: toolCall.id,
                type: this.mapToolCategoryToTaskType(tool.category),
                description: `${tool.name}: ${tool.description}`,
                tool: tool.name,
                parameters,
                priority: this.calculateTaskPriority(tool, parameters),
                retryCount: 0,
                maxRetries: this.getMaxRetries(tool.riskLevel),
                timeout: this.getTaskTimeout(tool.category),
                dependencies: [],
            };
            tasks.push(task);
        }
        return tasks;
    }
    mapToolCategoryToTaskType(category) {
        switch (category) {
            case 'file':
                return 'file_operation';
            case 'shell':
            case 'system':
                return 'shell_command';
            case 'network':
                return 'network';
            case 'analysis':
                return 'analysis';
            case 'generation':
                return 'generation';
            default:
                return 'file_operation';
        }
    }
    calculateTaskPriority(tool, parameters) {
        let priority = 5; // Default priority
        // Adjust based on risk level
        switch (tool.riskLevel) {
            case 'critical':
                priority += 3;
                break;
            case 'high':
                priority += 2;
                break;
            case 'medium':
                priority += 1;
                break;
            case 'low':
                priority += 0;
                break;
        }
        // Adjust based on tool category
        switch (tool.category) {
            case 'analysis':
                priority += 2; // Analysis tasks should run early
                break;
            case 'file':
                if (tool.name.includes('read') || tool.name.includes('list')) {
                    priority += 1; // Read operations before write operations
                }
                break;
            case 'shell':
                priority -= 1; // Shell commands can be risky, lower priority
                break;
        }
        return Math.max(1, Math.min(10, priority));
    }
    getMaxRetries(riskLevel) {
        switch (riskLevel) {
            case 'critical':
                return 1;
            case 'high':
                return 2;
            case 'medium':
                return 3;
            case 'low':
                return 3;
            default:
                return 2;
        }
    }
    getTaskTimeout(category) {
        switch (category) {
            case 'file':
                return 10000; // 10 seconds
            case 'shell':
                return 30000; // 30 seconds
            case 'system':
                return 15000; // 15 seconds
            case 'network':
                return 60000; // 60 seconds
            case 'analysis':
                return 20000; // 20 seconds
            case 'generation':
                return 45000; // 45 seconds
            default:
                return 30000;
        }
    }
    analyzeDependencies(tasks) {
        const dependencies = [];
        for (let i = 0; i < tasks.length; i++) {
            const task = tasks[i];
            const dependsOn = [];
            // Analyze dependencies based on task types and parameters
            for (let j = 0; j < i; j++) {
                const previousTask = tasks[j];
                if (this.hasDependency(task, previousTask)) {
                    dependsOn.push(previousTask.id);
                }
            }
            if (dependsOn.length > 0) {
                dependencies.push({
                    taskId: task.id,
                    dependsOn,
                });
                task.dependencies = dependsOn;
            }
        }
        return dependencies;
    }
    hasDependency(task, previousTask) {
        // File operation dependencies
        if (task.type === 'file_operation' && previousTask.type === 'file_operation') {
            const taskPath = task.parameters.path || task.parameters.source || task.parameters.destination;
            const prevPath = previousTask.parameters.path || previousTask.parameters.source || previousTask.parameters.destination;
            if (taskPath && prevPath) {
                // If tasks operate on the same file/directory
                if (taskPath === prevPath) {
                    // Write operations depend on previous operations on the same file
                    if (task.tool.includes('write') || task.tool.includes('create') || task.tool.includes('delete')) {
                        return true;
                    }
                }
                // If task operates on a file in a directory that previous task creates
                if (taskPath.startsWith(prevPath + '/') && previousTask.tool.includes('create')) {
                    return true;
                }
            }
        }
        // Shell command dependencies
        if (task.type === 'shell_command' && previousTask.type === 'file_operation') {
            // Shell commands that might use files created by previous tasks
            if (previousTask.tool.includes('create') || previousTask.tool.includes('write')) {
                return true;
            }
        }
        // Analysis tasks typically depend on file operations
        if (task.type === 'analysis' && (previousTask.type === 'file_operation' || previousTask.type === 'shell_command')) {
            return true;
        }
        // High-risk tasks should not run in parallel with other high-risk tasks
        const taskTool = this.toolRegistry.getTool(task.tool);
        const prevTaskTool = this.toolRegistry.getTool(previousTask.tool);
        if (taskTool?.riskLevel === 'critical' || prevTaskTool?.riskLevel === 'critical') {
            return true;
        }
        return false;
    }
    identifyParallelGroups(tasks, dependencies) {
        const parallelGroups = [];
        const processedTasks = new Set();
        // Group tasks by dependency level
        const dependencyLevels = this.calculateDependencyLevels(tasks, dependencies);
        const levelGroups = new Map();
        for (const [taskId, level] of dependencyLevels) {
            if (!levelGroups.has(level)) {
                levelGroups.set(level, []);
            }
            const task = tasks.find(t => t.id === taskId);
            if (task) {
                levelGroups.get(level).push(task);
            }
        }
        // Create parallel groups for each level
        for (const [level, levelTasks] of levelGroups) {
            if (levelTasks.length > 1) {
                // Filter tasks that can run in parallel
                const parallelTasks = levelTasks.filter(task => {
                    const tool = this.toolRegistry.getTool(task.tool);
                    return tool?.parallel && tool.riskLevel !== 'critical';
                });
                if (parallelTasks.length > 1) {
                    parallelGroups.push({
                        id: nanoid(),
                        taskIds: parallelTasks.map(t => t.id),
                        maxConcurrency: this.calculateMaxConcurrency(parallelTasks),
                    });
                }
            }
        }
        return parallelGroups;
    }
    calculateDependencyLevels(tasks, dependencies) {
        const levels = new Map();
        const dependencyMap = new Map();
        // Build dependency map
        for (const dep of dependencies) {
            dependencyMap.set(dep.taskId, dep.dependsOn);
        }
        // Calculate levels using topological sort
        const visited = new Set();
        const visiting = new Set();
        const calculateLevel = (taskId) => {
            if (visiting.has(taskId)) {
                throw new Error(`Circular dependency detected involving task ${taskId}`);
            }
            if (visited.has(taskId)) {
                return levels.get(taskId) || 0;
            }
            visiting.add(taskId);
            const deps = dependencyMap.get(taskId) || [];
            let maxDepLevel = -1;
            for (const depId of deps) {
                maxDepLevel = Math.max(maxDepLevel, calculateLevel(depId));
            }
            const level = maxDepLevel + 1;
            levels.set(taskId, level);
            visiting.delete(taskId);
            visited.add(taskId);
            return level;
        };
        for (const task of tasks) {
            if (!visited.has(task.id)) {
                calculateLevel(task.id);
            }
        }
        return levels;
    }
    calculateMaxConcurrency(tasks) {
        // Base concurrency on task types and system resources
        const fileOperations = tasks.filter(t => t.type === 'file_operation').length;
        const shellCommands = tasks.filter(t => t.type === 'shell_command').length;
        // Conservative approach: limit concurrent operations
        let maxConcurrency = Math.min(tasks.length, 3);
        // Reduce concurrency for risky operations
        if (shellCommands > 0) {
            maxConcurrency = Math.min(maxConcurrency, 2);
        }
        // Increase concurrency for safe file operations
        if (fileOperations === tasks.length && tasks.every(t => t.tool.includes('read') || t.tool.includes('list'))) {
            maxConcurrency = Math.min(tasks.length, 5);
        }
        return Math.max(1, maxConcurrency);
    }
    estimateExecutionTime(tasks, parallelGroups) {
        let totalTime = 0;
        const processedTasks = new Set();
        // Calculate time for parallel groups
        for (const group of parallelGroups) {
            const groupTasks = tasks.filter(t => group.taskIds.includes(t.id));
            const maxTaskTime = Math.max(...groupTasks.map(t => t.timeout));
            totalTime += maxTaskTime;
            group.taskIds.forEach(id => processedTasks.add(id));
        }
        // Calculate time for sequential tasks
        const sequentialTasks = tasks.filter(t => !processedTasks.has(t.id));
        totalTime += sequentialTasks.reduce((sum, task) => sum + task.timeout, 0);
        return totalTime;
    }
    optimizePlan(plan) {
        // Sort tasks by priority within each dependency level
        const sortedTasks = [...plan.tasks].sort((a, b) => {
            // First by dependency level, then by priority
            const aLevel = this.getTaskLevel(a.id, plan.dependencies);
            const bLevel = this.getTaskLevel(b.id, plan.dependencies);
            if (aLevel !== bLevel) {
                return aLevel - bLevel;
            }
            return b.priority - a.priority;
        });
        return {
            ...plan,
            tasks: sortedTasks,
        };
    }
    getTaskLevel(taskId, dependencies) {
        const dep = dependencies.find(d => d.taskId === taskId);
        return dep ? dep.dependsOn.length : 0;
    }
}
//# sourceMappingURL=planner.js.map