import { ProviderFactory } from '@/providers';
import { ToolRegistry } from '@/tools/tool-registry';
import { TaskPlanner } from './planner';
import { TaskExecutor } from './executor';
import { ContextManager } from '@/context/context-manager';
import { SessionManager } from '@/session/session-manager';
import { logger } from '@/utils/logger';
import { nanoid } from 'nanoid';
export class AutonomousAgent {
    provider;
    toolRegistry;
    planner;
    executor;
    contextManager;
    sessionManager;
    config;
    conversationHistory = [];
    constructor(config) {
        this.config = config;
        this.provider = ProviderFactory.createProvider(config.provider, {
            apiKey: process.env[`${config.provider.toUpperCase()}_API_KEY`] || '',
            model: config.model,
            temperature: config.temperature,
            maxTokens: config.maxTokens,
            timeout: config.timeout,
        });
        this.toolRegistry = ToolRegistry.getInstance();
        this.planner = new TaskPlanner(this.toolRegistry);
        this.executor = new TaskExecutor(this.toolRegistry);
        this.contextManager = ContextManager.getInstance();
        this.sessionManager = SessionManager.getInstance();
        this.initializeSystemPrompt();
    }
    initializeSystemPrompt() {
        const systemPrompt = `You are an autonomous AI agent with access to a comprehensive set of tools for file operations, shell commands, and system management. Your role is to:

1. **Understand user requests** and break them down into actionable tasks
2. **Plan execution** by creating detailed, step-by-step plans
3. **Execute autonomously** using available tools and functions
4. **Handle errors gracefully** with intelligent recovery mechanisms
5. **Learn from patterns** and improve over time

## Available Tool Categories:
- **File Operations**: read, write, create, delete, move, copy, search, grep, glob, permissions
- **Shell Commands**: execute commands, run scripts, manage processes, environment variables
- **System Management**: process control, environment inspection

## Key Principles:
- Always analyze the current context and project structure before acting
- Break complex tasks into smaller, manageable steps
- Use parallel execution when tasks are independent
- Implement proper error handling and recovery
- Learn from successful patterns and avoid repeating failures
- Provide clear, informative feedback about progress and results

## Context Awareness:
You have access to the current working directory, project structure, git information, dependencies, and session history. Use this context to make informed decisions.

## Safety:
- Always confirm destructive operations
- Use appropriate risk levels for different tools
- Implement rollback mechanisms where possible
- Respect file permissions and system boundaries

Remember: You are autonomous but responsible. Make decisions that align with the user's intent while maintaining system safety and integrity.`;
        this.conversationHistory = [
            { role: 'system', content: systemPrompt }
        ];
    }
    async processRequest(userInput) {
        logger.agent('Processing user request', { input: userInput });
        try {
            // Add user message to conversation
            this.conversationHistory.push({ role: 'user', content: userInput });
            // Get current context
            const context = this.contextManager.getCurrentContext();
            if (!context) {
                throw new Error('No active context. Please initialize a session first.');
            }
            // Add context information to the conversation
            const contextInfo = this.buildContextMessage(context);
            this.conversationHistory.push({ role: 'system', content: contextInfo });
            // Generate initial response and plan
            const planningResponse = await this.generatePlanningResponse(userInput, context);
            // Parse the response for tool calls (tasks)
            const plan = await this.planner.createExecutionPlan(planningResponse.toolCalls || [], context);
            logger.agent('Execution plan created', {
                taskCount: plan.tasks.length,
                parallelGroups: plan.parallelGroups.length
            });
            // Execute the plan
            const results = await this.executor.executePlan(plan, context);
            // Process results and generate summary
            const summary = await this.generateSummary(plan, results, context);
            // Update conversation history with results
            this.updateConversationWithResults(results);
            // Learn from the execution
            await this.learnFromExecution(plan, results, context);
            logger.agent('Request processing completed', {
                successfulTasks: results.filter(r => r.success).length,
                failedTasks: results.filter(r => !r.success).length
            });
            return { plan, results, summary };
        }
        catch (error) {
            logger.error('Failed to process request', error);
            throw error;
        }
    }
    async generatePlanningResponse(userInput, context) {
        const tools = this.toolRegistry.getToolsForLLM();
        const planningPrompt = `
User Request: ${userInput}

Current Context:
${this.contextManager.getContextSummary()}

Please analyze this request and create a detailed execution plan. Use the available tools to accomplish the user's goal. Consider:

1. What information do you need to gather first?
2. What are the main tasks to accomplish?
3. Which tasks can be executed in parallel?
4. What are the potential risks and how to mitigate them?
5. How to handle errors and provide recovery mechanisms?

Break down the request into specific tool calls that will accomplish the goal efficiently and safely.
`;
        this.conversationHistory.push({ role: 'user', content: planningPrompt });
        const response = await this.provider.generateResponse(this.conversationHistory, tools, false);
        this.conversationHistory.push({
            role: 'assistant',
            content: response.content,
            toolCalls: response.toolCalls
        });
        return response;
    }
    buildContextMessage(context) {
        const relevantMemories = this.contextManager.getRelevantMemories('current task', 5);
        const successfulPatterns = this.contextManager.getSuccessfulPatterns().slice(0, 3);
        return `
## Current Context Summary:
${this.contextManager.getContextSummary()}

## Relevant Memories:
${relevantMemories.map(m => `- ${m.content} (${m.type}, importance: ${m.importance})`).join('\n')}

## Successful Patterns:
${successfulPatterns.map(p => `- ${p.pattern} (used ${p.frequency} times)`).join('\n')}

## Recent History:
${context.history.slice(-3).map(h => `- ${h.command}: ${h.result.success ? 'SUCCESS' : 'FAILED'}`).join('\n')}
`;
    }
    async generateSummary(plan, results, context) {
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        const summaryPrompt = `
Please provide a concise summary of the execution results:

Plan: ${plan.tasks.length} tasks planned
Results: ${successCount} successful, ${failureCount} failed
Duration: ${totalDuration}ms

Task Results:
${results.map(r => `- ${r.taskId}: ${r.success ? 'SUCCESS' : 'FAILED'} ${r.error ? `(${r.error.message})` : ''}`).join('\n')}

Provide a user-friendly summary of what was accomplished and any issues encountered.
`;
        const response = await this.provider.generateResponse([
            { role: 'system', content: 'You are summarizing task execution results for the user.' },
            { role: 'user', content: summaryPrompt }
        ]);
        return response.content;
    }
    updateConversationWithResults(results) {
        const resultsSummary = results.map(result => {
            if (result.success) {
                return `Tool ${result.taskId} executed successfully`;
            }
            else {
                return `Tool ${result.taskId} failed: ${result.error?.message || 'Unknown error'}`;
            }
        }).join('\n');
        this.conversationHistory.push({
            role: 'system',
            content: `Execution Results:\n${resultsSummary}`
        });
    }
    async learnFromExecution(plan, results, context) {
        // Record successful patterns
        const successfulTasks = results.filter(r => r.success);
        const failedTasks = results.filter(r => !r.success);
        // Learn from successful task sequences
        if (successfulTasks.length > 1) {
            const pattern = successfulTasks.map(t => {
                const task = plan.tasks.find(pt => pt.id === t.taskId);
                return task?.tool;
            }).join(' -> ');
            this.contextManager.addPattern(pattern, true);
        }
        // Learn from failures
        for (const failure of failedTasks) {
            const task = plan.tasks.find(t => t.id === failure.taskId);
            if (task) {
                this.contextManager.addMemory(`Task ${task.tool} failed: ${failure.error?.message}`, 'error', 7, [task.tool, 'failure']);
            }
        }
        // Record general execution memory
        this.contextManager.addMemory(`Executed ${plan.tasks.length} tasks: ${successfulTasks.length} successful, ${failedTasks.length} failed`, 'fact', 6, ['execution', 'summary']);
        // Update session
        await this.sessionManager.updateCurrentSession({
            history: [
                ...context.history,
                {
                    id: nanoid(),
                    timestamp: new Date(),
                    command: `Plan execution: ${plan.tasks.length} tasks`,
                    result: {
                        taskId: plan.id,
                        success: failedTasks.length === 0,
                        output: { successCount: successfulTasks.length, failureCount: failedTasks.length },
                        duration: results.reduce((sum, r) => sum + r.duration, 0),
                        timestamp: new Date(),
                    },
                    context: { sessionId: context.sessionId, workingDirectory: context.workingDirectory },
                }
            ]
        });
    }
    async handleToolCall(toolCall, context) {
        try {
            const parameters = JSON.parse(toolCall.function.arguments);
            return await this.toolRegistry.executeTool(toolCall.function.name, parameters, context);
        }
        catch (error) {
            return {
                taskId: toolCall.id,
                success: false,
                output: null,
                error: error instanceof Error ? error : new Error(String(error)),
                duration: 0,
                timestamp: new Date(),
            };
        }
    }
    getConversationHistory() {
        return [...this.conversationHistory];
    }
    clearConversationHistory() {
        this.initializeSystemPrompt();
    }
    async streamResponse(userInput, onChunk) {
        this.conversationHistory.push({ role: 'user', content: userInput });
        const context = this.contextManager.getCurrentContext();
        if (context) {
            const contextInfo = this.buildContextMessage(context);
            this.conversationHistory.push({ role: 'system', content: contextInfo });
        }
        const tools = this.toolRegistry.getToolsForLLM();
        const generator = this.provider.generateStream(this.conversationHistory, tools, onChunk);
        let fullResponse = '';
        for await (const chunk of generator) {
            fullResponse += chunk;
        }
        // The generator returns the final response
        const finalResponse = await generator.return(undefined);
        this.conversationHistory.push({
            role: 'assistant',
            content: finalResponse.value.content,
            toolCalls: finalResponse.value.toolCalls,
        });
    }
}
//# sourceMappingURL=autonomous-agent.js.map