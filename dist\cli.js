#!/usr/bin/env node
import { Command } from 'commander';
import { config } from 'dotenv';
import chalk from 'chalk';
import { enquirer } from 'enquirer';
import { AutonomousAgent } from '@/agent/autonomous-agent';
import { SessionManager } from '@/session/session-manager';
import { StreamingUI } from '@/ui/streaming-ui';
import { ProviderFactory } from '@/providers';
import { logger } from '@/utils/logger';
import path from 'path';
import fs from 'fs-extra';
// Load environment variables
config();
const program = new Command();
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
program
    .name('ai-cli')
    .description('Autonomous AI-Powered CLI Tool System')
    .version(packageJson.version);
// Global options
program
    .option('-v, --verbose', 'Enable verbose output')
    .option('-q, --quiet', 'Suppress non-essential output')
    .option('--no-color', 'Disable colored output')
    .option('--config <path>', 'Path to configuration file');
// Main chat command
program
    .command('chat')
    .description('Start an interactive chat session with the AI agent')
    .option('-p, --provider <provider>', 'LLM provider (deepseek, openai, anthropic)', 'deepseek')
    .option('-m, --model <model>', 'Model to use')
    .option('-t, --temperature <number>', 'Temperature for generation', '0.7')
    .option('-s, --session <id>', 'Resume existing session')
    .option('-n, --new', 'Force create new session')
    .option('--stream', 'Enable streaming responses', true)
    .action(async (options) => {
    try {
        await runChatCommand(options);
    }
    catch (error) {
        logger.error('Chat command failed', error);
        process.exit(1);
    }
});
// Execute command
program
    .command('exec <prompt>')
    .description('Execute a single command/prompt')
    .option('-p, --provider <provider>', 'LLM provider', 'deepseek')
    .option('-m, --model <model>', 'Model to use')
    .option('-w, --working-dir <path>', 'Working directory', process.cwd())
    .option('--dry-run', 'Show plan without executing')
    .action(async (prompt, options) => {
    try {
        await runExecCommand(prompt, options);
    }
    catch (error) {
        logger.error('Exec command failed', error);
        process.exit(1);
    }
});
// Session management commands
const sessionCmd = program
    .command('session')
    .description('Manage AI agent sessions');
sessionCmd
    .command('list')
    .description('List all sessions')
    .action(async () => {
    try {
        await listSessions();
    }
    catch (error) {
        logger.error('Failed to list sessions', error);
        process.exit(1);
    }
});
sessionCmd
    .command('load <sessionId>')
    .description('Load a specific session')
    .action(async (sessionId) => {
    try {
        await loadSession(sessionId);
    }
    catch (error) {
        logger.error('Failed to load session', error);
        process.exit(1);
    }
});
sessionCmd
    .command('delete <sessionId>')
    .description('Delete a session')
    .action(async (sessionId) => {
    try {
        await deleteSession(sessionId);
    }
    catch (error) {
        logger.error('Failed to delete session', error);
        process.exit(1);
    }
});
sessionCmd
    .command('export <sessionId> <path>')
    .description('Export a session to file')
    .action(async (sessionId, exportPath) => {
    try {
        await exportSession(sessionId, exportPath);
    }
    catch (error) {
        logger.error('Failed to export session', error);
        process.exit(1);
    }
});
sessionCmd
    .command('import <path>')
    .description('Import a session from file')
    .action(async (importPath) => {
    try {
        await importSession(importPath);
    }
    catch (error) {
        logger.error('Failed to import session', error);
        process.exit(1);
    }
});
// Configuration commands
const configCmd = program
    .command('config')
    .description('Manage configuration');
configCmd
    .command('setup')
    .description('Interactive configuration setup')
    .action(async () => {
    try {
        await setupConfiguration();
    }
    catch (error) {
        logger.error('Configuration setup failed', error);
        process.exit(1);
    }
});
configCmd
    .command('providers')
    .description('List available providers and models')
    .action(() => {
    listProviders();
});
// Parse command line arguments
program.parse();
async function runChatCommand(options) {
    const ui = new StreamingUI({
        theme: 'auto',
        verbose: options.verbose || program.opts().verbose,
        showProgress: true,
        showTimestamps: true,
        maxOutputLines: 50,
    });
    ui.displayInfo('Starting AI CLI Agent...');
    // Setup agent configuration
    const agentConfig = await setupAgentConfig(options);
    const agent = new AutonomousAgent(agentConfig);
    // Setup session
    const sessionManager = SessionManager.getInstance();
    let session;
    if (options.session && !options.new) {
        try {
            session = await sessionManager.loadSession(options.session);
            ui.displaySuccess(`Loaded session: ${session.name}`);
        }
        catch (error) {
            ui.displayWarning(`Failed to load session ${options.session}, creating new one`);
            session = await sessionManager.createSession(`Chat Session ${new Date().toLocaleString()}`, agentConfig);
        }
    }
    else {
        session = await sessionManager.createSession(`Chat Session ${new Date().toLocaleString()}`, agentConfig);
        ui.displaySuccess(`Created new session: ${session.name}`);
    }
    ui.displayInfo(`Working directory: ${session.context.workingDirectory}`);
    ui.displayInfo(`Provider: ${agentConfig.provider} (${agentConfig.model})`);
    ui.newLine();
    // Interactive chat loop
    while (true) {
        try {
            const { input } = await enquirer.prompt({
                type: 'input',
                name: 'input',
                message: chalk.cyan('You:'),
            });
            if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
                break;
            }
            if (input.toLowerCase() === 'clear') {
                ui.clear();
                continue;
            }
            if (input.toLowerCase().startsWith('cd ')) {
                const newDir = input.slice(3).trim();
                try {
                    await sessionManager.switchWorkingDirectory(newDir);
                    ui.displaySuccess(`Changed directory to: ${newDir}`);
                }
                catch (error) {
                    ui.displayError(`Failed to change directory: ${error instanceof Error ? error.message : String(error)}`);
                }
                continue;
            }
            if (input.trim() === '') {
                continue;
            }
            // Process the request
            const thinking = ui.displayThinking('Planning and executing...');
            try {
                if (options.stream) {
                    await agent.streamResponse(input, (chunk) => {
                        thinking.stop();
                        process.stdout.write(chalk.green(chunk));
                    });
                    ui.newLine();
                }
                else {
                    const result = await agent.processRequest(input);
                    thinking.stop();
                    ui.startExecution(result.plan);
                    ui.completeExecution(result.results);
                    console.log(chalk.green('\nAgent: ') + result.summary);
                }
            }
            catch (error) {
                thinking.stop();
                ui.displayError(`Error: ${error instanceof Error ? error.message : String(error)}`);
            }
            ui.newLine();
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('User force closed')) {
                break;
            }
            throw error;
        }
    }
    ui.displayInfo('Goodbye!');
}
async function runExecCommand(prompt, options) {
    const ui = new StreamingUI({
        theme: 'auto',
        verbose: options.verbose || program.opts().verbose,
        showProgress: true,
        showTimestamps: true,
        maxOutputLines: 50,
    });
    // Setup agent configuration
    const agentConfig = await setupAgentConfig(options);
    const agent = new AutonomousAgent(agentConfig);
    // Setup temporary session
    const sessionManager = SessionManager.getInstance();
    const session = await sessionManager.createSession(`Exec Session ${new Date().toLocaleString()}`, agentConfig, options.workingDir || process.cwd(), false // Non-persistent
    );
    ui.displayInfo(`Executing: ${prompt}`);
    ui.displayInfo(`Working directory: ${session.context.workingDirectory}`);
    ui.newLine();
    try {
        const result = await agent.processRequest(prompt);
        if (options.dryRun) {
            ui.displayInfo('Dry run mode - showing plan only:');
            ui.startExecution(result.plan);
            return;
        }
        ui.startExecution(result.plan);
        ui.completeExecution(result.results);
        console.log(chalk.green('\nResult: ') + result.summary);
    }
    catch (error) {
        ui.displayError(`Error: ${error instanceof Error ? error.message : String(error)}`);
        process.exit(1);
    }
}
async function setupAgentConfig(options) {
    const provider = options.provider;
    if (!ProviderFactory.getSupportedProviders().includes(provider)) {
        throw new Error(`Unsupported provider: ${provider}`);
    }
    const model = options.model || ProviderFactory.getDefaultModel(provider);
    const temperature = parseFloat(options.temperature || '0.7');
    // Validate API key
    const apiKeyEnvVar = `${provider.toUpperCase()}_API_KEY`;
    if (!process.env[apiKeyEnvVar] && provider !== 'ollama') {
        throw new Error(`Missing API key. Please set ${apiKeyEnvVar} environment variable.`);
    }
    return {
        provider,
        model,
        temperature,
        maxTokens: 4096,
        timeout: 30000,
    };
}
async function listSessions() {
    const sessionManager = SessionManager.getInstance();
    const sessions = await sessionManager.listSessions();
    if (sessions.length === 0) {
        console.log(chalk.gray('No sessions found.'));
        return;
    }
    console.log(chalk.bold('\n📋 Sessions:'));
    sessions.forEach((session, index) => {
        const size = (session.size / 1024).toFixed(1);
        console.log(`${index + 1}. ${chalk.cyan(session.name)} ${chalk.gray(`(${session.id})`)}\n` +
            `   Created: ${session.created.toLocaleString()}\n` +
            `   Last accessed: ${session.lastAccessed.toLocaleString()}\n` +
            `   Directory: ${session.workingDirectory}\n` +
            `   Size: ${size} KB\n`);
    });
}
async function loadSession(sessionId) {
    const sessionManager = SessionManager.getInstance();
    const session = await sessionManager.loadSession(sessionId);
    console.log(chalk.green(`✅ Loaded session: ${session.name}`));
    console.log(chalk.gray(`   Directory: ${session.context.workingDirectory}`));
    console.log(chalk.gray(`   Provider: ${session.config.provider} (${session.config.model})`));
}
async function deleteSession(sessionId) {
    const sessionManager = SessionManager.getInstance();
    const { confirm } = await enquirer.prompt({
        type: 'confirm',
        name: 'confirm',
        message: `Are you sure you want to delete session ${sessionId}?`,
    });
    if (confirm) {
        await sessionManager.deleteSession(sessionId);
        console.log(chalk.green(`✅ Session ${sessionId} deleted`));
    }
    else {
        console.log(chalk.gray('Cancelled'));
    }
}
async function exportSession(sessionId, exportPath) {
    const sessionManager = SessionManager.getInstance();
    await sessionManager.exportSession(sessionId, exportPath);
    console.log(chalk.green(`✅ Session exported to: ${exportPath}`));
}
async function importSession(importPath) {
    const sessionManager = SessionManager.getInstance();
    const session = await sessionManager.importSession(importPath);
    console.log(chalk.green(`✅ Session imported: ${session.name} (${session.id})`));
}
async function setupConfiguration() {
    console.log(chalk.bold('\n🔧 AI CLI Agent Configuration Setup\n'));
    const providers = ProviderFactory.getSupportedProviders();
    const { provider } = await enquirer.prompt({
        type: 'select',
        name: 'provider',
        message: 'Select LLM provider:',
        choices: providers.map(p => ({ name: p, value: p })),
    });
    const models = ProviderFactory.getProviderModels(provider);
    const { model } = await enquirer.prompt({
        type: 'select',
        name: 'model',
        message: 'Select model:',
        choices: models.map(m => ({ name: m, value: m })),
    });
    const { temperature } = await enquirer.prompt({
        type: 'numeral',
        name: 'temperature',
        message: 'Temperature (0.0 - 2.0):',
        initial: 0.7,
        min: 0,
        max: 2,
    });
    // Check for API key
    const apiKeyEnvVar = `${provider.toUpperCase()}_API_KEY`;
    if (!process.env[apiKeyEnvVar] && provider !== 'ollama') {
        console.log(chalk.yellow(`\n⚠️  Please set your API key:`));
        console.log(chalk.gray(`   export ${apiKeyEnvVar}="your-api-key-here"`));
    }
    console.log(chalk.green('\n✅ Configuration completed!'));
    console.log(chalk.gray(`   Provider: ${provider}`));
    console.log(chalk.gray(`   Model: ${model}`));
    console.log(chalk.gray(`   Temperature: ${temperature}`));
}
function listProviders() {
    console.log(chalk.bold('\n🔌 Available Providers:\n'));
    const providers = ProviderFactory.getSupportedProviders();
    providers.forEach(provider => {
        const models = ProviderFactory.getProviderModels(provider);
        const defaultModel = ProviderFactory.getDefaultModel(provider);
        console.log(chalk.cyan(`${provider.toUpperCase()}:`));
        console.log(chalk.gray(`  Default: ${defaultModel}`));
        console.log(chalk.gray(`  Models: ${models.join(', ')}`));
        console.log(chalk.gray(`  API Key: ${provider.toUpperCase()}_API_KEY\n`));
    });
}
// Handle uncaught errors
process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection', { reason, promise });
    process.exit(1);
});
// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log(chalk.yellow('\n\n👋 Goodbye!'));
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log(chalk.yellow('\n\n👋 Goodbye!'));
    process.exit(0);
});
//# sourceMappingURL=cli.js.map