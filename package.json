{"name": "ai-cli-agent", "version": "1.0.0", "description": "Autonomous AI-Powered CLI Tool System", "main": "dist/cli.js", "bin": {"ai-cli": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.ts", "start": "node dist/cli.js", "watch": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "cli", "autonomous", "agent", "llm", "typescript"], "author": "AI CLI Agent", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@google/generative-ai": "^0.15.0", "@mistralai/mistralai": "^0.4.0", "axios": "^1.7.2", "chalk": "^5.3.0", "commander": "^12.1.0", "conf": "^12.0.0", "dotenv": "^16.4.5", "enquirer": "^2.4.1", "execa": "^8.0.1", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "listr2": "^8.2.1", "lodash": "^4.17.21", "module-alias": "^2.2.3", "nanoid": "^5.0.7", "node-fetch": "^3.3.2", "openai": "^4.52.7", "ora": "^8.0.1", "p-limit": "^5.0.0", "p-queue": "^8.0.1", "rxjs": "^7.8.1", "strip-ansi": "^7.1.0", "tree-kill": "^1.2.2", "winston": "^3.13.0", "ws": "^8.17.1", "zod": "^3.23.8"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.5", "@types/node": "^20.14.9", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.3.2", "ts-jest": "^29.1.5", "tsx": "^4.15.7", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "_moduleAliases": {"@": "dist"}}