"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicProvider = void 0;
const llm_provider_1 = require("./llm-provider");
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
class AnthropicProvider extends llm_provider_1.LLMProvider {
    client;
    constructor(config) {
        super(config);
        this.client = new sdk_1.default({
            apiKey: this.apiKey,
            baseURL: config.baseUrl,
            timeout: this.timeout,
        });
    }
    async generateResponse(messages, tools, stream = false) {
        try {
            const { system, messages: anthropicMessages } = this.formatAnthropicMessages(messages);
            const params = {
                model: this.model,
                messages: anthropicMessages,
                max_tokens: this.maxTokens,
                temperature: this.temperature,
                stream,
            };
            if (system) {
                params.system = system;
            }
            if (tools && tools.length > 0) {
                params.tools = this.formatAnthropicTools(tools);
            }
            const response = await this.client.messages.create(params);
            let content = '';
            let toolCalls = [];
            if (Array.isArray(response.content)) {
                for (const block of response.content) {
                    if (block.type === 'text') {
                        content += block.text;
                    }
                    else if (block.type === 'tool_use') {
                        toolCalls.push({
                            id: block.id,
                            type: 'function',
                            function: {
                                name: block.name,
                                arguments: JSON.stringify(block.input),
                            },
                        });
                    }
                }
            }
            return {
                content,
                toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
                usage: response.usage ? {
                    promptTokens: response.usage.input_tokens,
                    completionTokens: response.usage.output_tokens,
                    totalTokens: response.usage.input_tokens + response.usage.output_tokens,
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async *generateStream(messages, tools, onChunk) {
        try {
            const { system, messages: anthropicMessages } = this.formatAnthropicMessages(messages);
            const params = {
                model: this.model,
                messages: anthropicMessages,
                max_tokens: this.maxTokens,
                temperature: this.temperature,
                stream: true,
            };
            if (system) {
                params.system = system;
            }
            if (tools && tools.length > 0) {
                params.tools = this.formatAnthropicTools(tools);
            }
            const stream = await this.client.messages.create(params);
            let fullContent = '';
            let toolCalls = [];
            let usage;
            for await (const event of stream) {
                if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
                    const chunk = event.delta.text;
                    fullContent += chunk;
                    if (onChunk)
                        onChunk(chunk);
                    yield chunk;
                }
                else if (event.type === 'content_block_start' && event.content_block.type === 'tool_use') {
                    toolCalls.push({
                        id: event.content_block.id,
                        type: 'function',
                        function: {
                            name: event.content_block.name,
                            arguments: JSON.stringify(event.content_block.input),
                        },
                    });
                }
                else if (event.type === 'message_delta' && event.usage) {
                    usage = event.usage;
                }
            }
            return {
                content: fullContent,
                toolCalls: toolCalls.length > 0 ? this.parseToolCalls(toolCalls) : undefined,
                usage: usage ? {
                    promptTokens: usage.input_tokens || 0,
                    completionTokens: usage.output_tokens || 0,
                    totalTokens: (usage.input_tokens || 0) + (usage.output_tokens || 0),
                } : undefined,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async validateConnection() {
        try {
            await this.client.messages.create({
                model: this.model,
                messages: [{ role: 'user', content: 'test' }],
                max_tokens: 1,
            });
            return true;
        }
        catch {
            return false;
        }
    }
    formatAnthropicMessages(messages) {
        let system;
        const anthropicMessages = [];
        for (const msg of messages) {
            if (msg.role === 'system') {
                system = msg.content;
            }
            else if (msg.role === 'tool') {
                // Handle tool response
                anthropicMessages.push({
                    role: 'user',
                    content: [
                        {
                            type: 'tool_result',
                            tool_use_id: msg.toolCallId,
                            content: msg.content,
                        },
                    ],
                });
            }
            else {
                anthropicMessages.push({
                    role: msg.role,
                    content: msg.content,
                });
            }
        }
        return { system, messages: anthropicMessages };
    }
    formatAnthropicTools(tools) {
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            input_schema: {
                type: 'object',
                properties: tool.parameters.reduce((acc, param) => {
                    acc[param.name] = {
                        type: param.type,
                        description: param.description,
                        ...(param.enum && { enum: param.enum }),
                    };
                    return acc;
                }, {}),
                required: tool.parameters.filter((p) => p.required).map((p) => p.name),
            },
        }));
    }
}
exports.AnthropicProvider = AnthropicProvider;
//# sourceMappingURL=anthropic.js.map