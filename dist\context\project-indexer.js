import fs from 'fs-extra';
import path from 'path';
import glob from 'fast-glob';
import { execa } from 'execa';
import { logger } from '@/utils/logger';
export class ProjectIndexer {
    ignoredPatterns = [
        'node_modules/**',
        '.git/**',
        'dist/**',
        'build/**',
        'target/**',
        '.next/**',
        '.nuxt/**',
        'coverage/**',
        '*.log',
        '.DS_Store',
        'Thumbs.db',
        '*.tmp',
        '*.temp',
    ];
    async indexProject(rootPath) {
        const absoluteRoot = path.resolve(rootPath);
        logger.debug('Starting project indexing', { rootPath: absoluteRoot });
        const [files, directories, gitInfo, packageInfo, dependencies] = await Promise.all([
            this.indexFiles(absoluteRoot),
            this.indexDirectories(absoluteRoot),
            this.getGitInfo(absoluteRoot),
            this.getPackageInfo(absoluteRoot),
            this.getDependencies(absoluteRoot),
        ]);
        const structure = {
            root: absoluteRoot,
            files,
            directories,
            gitInfo,
            packageInfo,
            dependencies,
        };
        logger.debug('Project indexing completed', {
            fileCount: files.length,
            directoryCount: directories.length,
            hasGit: !!gitInfo,
            hasPackage: !!packageInfo,
            dependencyCount: dependencies.length,
        });
        return structure;
    }
    async indexFiles(rootPath) {
        try {
            const patterns = ['**/*'];
            const files = await glob(patterns, {
                cwd: rootPath,
                ignore: this.ignoredPatterns,
                onlyFiles: true,
                absolute: false,
                stats: true,
            });
            const fileInfos = [];
            for (const file of files) {
                try {
                    const filePath = path.join(rootPath, file.path);
                    const stats = file.stats || await fs.stat(filePath);
                    const fileInfo = {
                        path: file.path,
                        name: path.basename(file.path),
                        extension: path.extname(file.path),
                        size: stats.size,
                        lastModified: stats.mtime,
                        type: stats.isDirectory() ? 'directory' : stats.isSymbolicLink() ? 'symlink' : 'file',
                        permissions: stats.mode.toString(8),
                    };
                    fileInfos.push(fileInfo);
                }
                catch (error) {
                    logger.warn(`Failed to stat file: ${file.path}`, error);
                }
            }
            return fileInfos.sort((a, b) => a.path.localeCompare(b.path));
        }
        catch (error) {
            logger.error('Failed to index files', error);
            return [];
        }
    }
    async indexDirectories(rootPath) {
        try {
            const patterns = ['**/'];
            const directories = await glob(patterns, {
                cwd: rootPath,
                ignore: this.ignoredPatterns,
                onlyDirectories: true,
                absolute: false,
            });
            const directoryInfos = [];
            for (const dir of directories) {
                try {
                    const dirPath = path.join(rootPath, dir);
                    const children = await fs.readdir(dirPath);
                    const stats = await fs.stat(dirPath);
                    const directoryInfo = {
                        path: dir,
                        name: path.basename(dir),
                        children: children.filter(child => !child.startsWith('.')),
                        size: children.length,
                    };
                    directoryInfos.push(directoryInfo);
                }
                catch (error) {
                    logger.warn(`Failed to read directory: ${dir}`, error);
                }
            }
            return directoryInfos.sort((a, b) => a.path.localeCompare(b.path));
        }
        catch (error) {
            logger.error('Failed to index directories', error);
            return [];
        }
    }
    async getGitInfo(rootPath) {
        try {
            const gitDir = path.join(rootPath, '.git');
            if (!await fs.pathExists(gitDir)) {
                return undefined;
            }
            const [branchResult, commitResult, remoteResult, statusResult] = await Promise.allSettled([
                execa('git', ['branch', '--show-current'], { cwd: rootPath }),
                execa('git', ['rev-parse', 'HEAD'], { cwd: rootPath }),
                execa('git', ['remote', 'get-url', 'origin'], { cwd: rootPath }),
                execa('git', ['status', '--porcelain'], { cwd: rootPath }),
            ]);
            const gitInfo = {
                branch: branchResult.status === 'fulfilled' ? branchResult.value.stdout.trim() : 'unknown',
                commit: commitResult.status === 'fulfilled' ? commitResult.value.stdout.trim() : 'unknown',
                remote: remoteResult.status === 'fulfilled' ? remoteResult.value.stdout.trim() : undefined,
                status: statusResult.status === 'fulfilled'
                    ? statusResult.value.stdout.split('\n').filter(line => line.trim())
                    : [],
            };
            return gitInfo;
        }
        catch (error) {
            logger.debug('Failed to get git info', error);
            return undefined;
        }
    }
    async getPackageInfo(rootPath) {
        // Try different package files
        const packageFiles = [
            { file: 'package.json', type: 'npm' },
            { file: 'pyproject.toml', type: 'python' },
            { file: 'requirements.txt', type: 'python' },
            { file: 'Cargo.toml', type: 'rust' },
            { file: 'go.mod', type: 'go' },
            { file: 'composer.json', type: 'other' },
        ];
        for (const { file, type } of packageFiles) {
            const filePath = path.join(rootPath, file);
            if (await fs.pathExists(filePath)) {
                try {
                    if (file === 'package.json') {
                        return await this.parsePackageJson(filePath);
                    }
                    else if (file === 'pyproject.toml') {
                        return await this.parsePyprojectToml(filePath);
                    }
                    else if (file === 'requirements.txt') {
                        return await this.parseRequirementsTxt(filePath);
                    }
                    else if (file === 'Cargo.toml') {
                        return await this.parseCargoToml(filePath);
                    }
                    else if (file === 'go.mod') {
                        return await this.parseGoMod(filePath);
                    }
                }
                catch (error) {
                    logger.warn(`Failed to parse ${file}`, error);
                }
            }
        }
        return undefined;
    }
    async parsePackageJson(filePath) {
        const content = await fs.readJson(filePath);
        return {
            name: content.name || 'unknown',
            version: content.version || '0.0.0',
            type: 'npm',
            dependencies: {
                ...content.dependencies,
                ...content.devDependencies,
            },
            scripts: content.scripts || {},
        };
    }
    async parsePyprojectToml(filePath) {
        const content = await fs.readFile(filePath, 'utf8');
        // Basic TOML parsing for project info
        const nameMatch = content.match(/name\s*=\s*"([^"]+)"/);
        const versionMatch = content.match(/version\s*=\s*"([^"]+)"/);
        return {
            name: nameMatch?.[1] || 'unknown',
            version: versionMatch?.[1] || '0.0.0',
            type: 'python',
            dependencies: {},
            scripts: {},
        };
    }
    async parseRequirementsTxt(filePath) {
        const content = await fs.readFile(filePath, 'utf8');
        const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
        const dependencies = {};
        lines.forEach(line => {
            const match = line.match(/^([^>=<]+)([>=<].+)?$/);
            if (match) {
                dependencies[match[1].trim()] = match[2]?.trim() || '*';
            }
        });
        return {
            name: path.basename(path.dirname(filePath)),
            version: '0.0.0',
            type: 'python',
            dependencies,
            scripts: {},
        };
    }
    async parseCargoToml(filePath) {
        const content = await fs.readFile(filePath, 'utf8');
        const nameMatch = content.match(/name\s*=\s*"([^"]+)"/);
        const versionMatch = content.match(/version\s*=\s*"([^"]+)"/);
        return {
            name: nameMatch?.[1] || 'unknown',
            version: versionMatch?.[1] || '0.0.0',
            type: 'rust',
            dependencies: {},
            scripts: {},
        };
    }
    async parseGoMod(filePath) {
        const content = await fs.readFile(filePath, 'utf8');
        const moduleMatch = content.match(/module\s+(.+)/);
        const goMatch = content.match(/go\s+(.+)/);
        return {
            name: moduleMatch?.[1] || 'unknown',
            version: goMatch?.[1] || '0.0.0',
            type: 'go',
            dependencies: {},
            scripts: {},
        };
    }
    async getDependencies(rootPath) {
        const dependencies = new Set();
        // Check for common dependency files
        const dependencyFiles = [
            'package.json',
            'requirements.txt',
            'Cargo.toml',
            'go.mod',
            'composer.json',
        ];
        for (const file of dependencyFiles) {
            const filePath = path.join(rootPath, file);
            if (await fs.pathExists(filePath)) {
                dependencies.add(file);
            }
        }
        // Check for lock files
        const lockFiles = [
            'package-lock.json',
            'yarn.lock',
            'pnpm-lock.yaml',
            'poetry.lock',
            'Cargo.lock',
            'go.sum',
            'composer.lock',
        ];
        for (const file of lockFiles) {
            const filePath = path.join(rootPath, file);
            if (await fs.pathExists(filePath)) {
                dependencies.add(file);
            }
        }
        return Array.from(dependencies);
    }
}
//# sourceMappingURL=project-indexer.js.map