"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectIndexer = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const fast_glob_1 = __importDefault(require("fast-glob"));
const execa_1 = require("execa");
const logger_1 = require("@/utils/logger");
class ProjectIndexer {
    ignoredPatterns = [
        'node_modules/**',
        '.git/**',
        'dist/**',
        'build/**',
        'target/**',
        '.next/**',
        '.nuxt/**',
        'coverage/**',
        '*.log',
        '.DS_Store',
        'Thumbs.db',
        '*.tmp',
        '*.temp',
    ];
    async indexProject(rootPath) {
        const absoluteRoot = path_1.default.resolve(rootPath);
        logger_1.logger.debug('Starting project indexing', { rootPath: absoluteRoot });
        const [files, directories, gitInfo, packageInfo, dependencies] = await Promise.all([
            this.indexFiles(absoluteRoot),
            this.indexDirectories(absoluteRoot),
            this.getGitInfo(absoluteRoot),
            this.getPackageInfo(absoluteRoot),
            this.getDependencies(absoluteRoot),
        ]);
        const structure = {
            root: absoluteRoot,
            files,
            directories,
            gitInfo,
            packageInfo,
            dependencies,
        };
        logger_1.logger.debug('Project indexing completed', {
            fileCount: files.length,
            directoryCount: directories.length,
            hasGit: !!gitInfo,
            hasPackage: !!packageInfo,
            dependencyCount: dependencies.length,
        });
        return structure;
    }
    async indexFiles(rootPath) {
        try {
            const patterns = ['**/*'];
            const files = await (0, fast_glob_1.default)(patterns, {
                cwd: rootPath,
                ignore: this.ignoredPatterns,
                onlyFiles: true,
                absolute: false,
                stats: true,
            });
            const fileInfos = [];
            for (const file of files) {
                try {
                    const filePath = path_1.default.join(rootPath, file.path);
                    const stats = file.stats || await fs_extra_1.default.stat(filePath);
                    const fileInfo = {
                        path: file.path,
                        name: path_1.default.basename(file.path),
                        extension: path_1.default.extname(file.path),
                        size: stats.size,
                        lastModified: stats.mtime,
                        type: stats.isDirectory() ? 'directory' : stats.isSymbolicLink() ? 'symlink' : 'file',
                        permissions: stats.mode.toString(8),
                    };
                    fileInfos.push(fileInfo);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to stat file: ${file.path}`, error);
                }
            }
            return fileInfos.sort((a, b) => a.path.localeCompare(b.path));
        }
        catch (error) {
            logger_1.logger.error('Failed to index files', error);
            return [];
        }
    }
    async indexDirectories(rootPath) {
        try {
            const patterns = ['**/'];
            const directories = await (0, fast_glob_1.default)(patterns, {
                cwd: rootPath,
                ignore: this.ignoredPatterns,
                onlyDirectories: true,
                absolute: false,
            });
            const directoryInfos = [];
            for (const dir of directories) {
                try {
                    const dirPath = path_1.default.join(rootPath, dir);
                    const children = await fs_extra_1.default.readdir(dirPath);
                    const stats = await fs_extra_1.default.stat(dirPath);
                    const directoryInfo = {
                        path: dir,
                        name: path_1.default.basename(dir),
                        children: children.filter(child => !child.startsWith('.')),
                        size: children.length,
                    };
                    directoryInfos.push(directoryInfo);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to read directory: ${dir}`, error);
                }
            }
            return directoryInfos.sort((a, b) => a.path.localeCompare(b.path));
        }
        catch (error) {
            logger_1.logger.error('Failed to index directories', error);
            return [];
        }
    }
    async getGitInfo(rootPath) {
        try {
            const gitDir = path_1.default.join(rootPath, '.git');
            if (!await fs_extra_1.default.pathExists(gitDir)) {
                return undefined;
            }
            const [branchResult, commitResult, remoteResult, statusResult] = await Promise.allSettled([
                (0, execa_1.execa)('git', ['branch', '--show-current'], { cwd: rootPath }),
                (0, execa_1.execa)('git', ['rev-parse', 'HEAD'], { cwd: rootPath }),
                (0, execa_1.execa)('git', ['remote', 'get-url', 'origin'], { cwd: rootPath }),
                (0, execa_1.execa)('git', ['status', '--porcelain'], { cwd: rootPath }),
            ]);
            const gitInfo = {
                branch: branchResult.status === 'fulfilled' ? branchResult.value.stdout.trim() : 'unknown',
                commit: commitResult.status === 'fulfilled' ? commitResult.value.stdout.trim() : 'unknown',
                remote: remoteResult.status === 'fulfilled' ? remoteResult.value.stdout.trim() : undefined,
                status: statusResult.status === 'fulfilled'
                    ? statusResult.value.stdout.split('\n').filter(line => line.trim())
                    : [],
            };
            return gitInfo;
        }
        catch (error) {
            logger_1.logger.debug('Failed to get git info', error);
            return undefined;
        }
    }
    async getPackageInfo(rootPath) {
        // Try different package files
        const packageFiles = [
            { file: 'package.json', type: 'npm' },
            { file: 'pyproject.toml', type: 'python' },
            { file: 'requirements.txt', type: 'python' },
            { file: 'Cargo.toml', type: 'rust' },
            { file: 'go.mod', type: 'go' },
            { file: 'composer.json', type: 'other' },
        ];
        for (const { file, type } of packageFiles) {
            const filePath = path_1.default.join(rootPath, file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                try {
                    if (file === 'package.json') {
                        return await this.parsePackageJson(filePath);
                    }
                    else if (file === 'pyproject.toml') {
                        return await this.parsePyprojectToml(filePath);
                    }
                    else if (file === 'requirements.txt') {
                        return await this.parseRequirementsTxt(filePath);
                    }
                    else if (file === 'Cargo.toml') {
                        return await this.parseCargoToml(filePath);
                    }
                    else if (file === 'go.mod') {
                        return await this.parseGoMod(filePath);
                    }
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to parse ${file}`, error);
                }
            }
        }
        return undefined;
    }
    async parsePackageJson(filePath) {
        const content = await fs_extra_1.default.readJson(filePath);
        return {
            name: content.name || 'unknown',
            version: content.version || '0.0.0',
            type: 'npm',
            dependencies: {
                ...content.dependencies,
                ...content.devDependencies,
            },
            scripts: content.scripts || {},
        };
    }
    async parsePyprojectToml(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        // Basic TOML parsing for project info
        const nameMatch = content.match(/name\s*=\s*"([^"]+)"/);
        const versionMatch = content.match(/version\s*=\s*"([^"]+)"/);
        return {
            name: nameMatch?.[1] || 'unknown',
            version: versionMatch?.[1] || '0.0.0',
            type: 'python',
            dependencies: {},
            scripts: {},
        };
    }
    async parseRequirementsTxt(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
        const dependencies = {};
        lines.forEach(line => {
            const match = line.match(/^([^>=<]+)([>=<].+)?$/);
            if (match) {
                dependencies[match[1].trim()] = match[2]?.trim() || '*';
            }
        });
        return {
            name: path_1.default.basename(path_1.default.dirname(filePath)),
            version: '0.0.0',
            type: 'python',
            dependencies,
            scripts: {},
        };
    }
    async parseCargoToml(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const nameMatch = content.match(/name\s*=\s*"([^"]+)"/);
        const versionMatch = content.match(/version\s*=\s*"([^"]+)"/);
        return {
            name: nameMatch?.[1] || 'unknown',
            version: versionMatch?.[1] || '0.0.0',
            type: 'rust',
            dependencies: {},
            scripts: {},
        };
    }
    async parseGoMod(filePath) {
        const content = await fs_extra_1.default.readFile(filePath, 'utf8');
        const moduleMatch = content.match(/module\s+(.+)/);
        const goMatch = content.match(/go\s+(.+)/);
        return {
            name: moduleMatch?.[1] || 'unknown',
            version: goMatch?.[1] || '0.0.0',
            type: 'go',
            dependencies: {},
            scripts: {},
        };
    }
    async getDependencies(rootPath) {
        const dependencies = new Set();
        // Check for common dependency files
        const dependencyFiles = [
            'package.json',
            'requirements.txt',
            'Cargo.toml',
            'go.mod',
            'composer.json',
        ];
        for (const file of dependencyFiles) {
            const filePath = path_1.default.join(rootPath, file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                dependencies.add(file);
            }
        }
        // Check for lock files
        const lockFiles = [
            'package-lock.json',
            'yarn.lock',
            'pnpm-lock.yaml',
            'poetry.lock',
            'Cargo.lock',
            'go.sum',
            'composer.lock',
        ];
        for (const file of lockFiles) {
            const filePath = path_1.default.join(rootPath, file);
            if (await fs_extra_1.default.pathExists(filePath)) {
                dependencies.add(file);
            }
        }
        return Array.from(dependencies);
    }
}
exports.ProjectIndexer = ProjectIndexer;
//# sourceMappingURL=project-indexer.js.map