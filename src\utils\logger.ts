import winston from 'winston';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs-extra';

export class Logger {
  private static instance: Logger;
  private logger!: winston.Logger;
  private logDir: string;

  private constructor() {
    this.logDir = path.join(process.cwd(), '.ai-cli', 'logs');
    this.ensureLogDirectory();
    this.setupLogger();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private ensureLogDirectory(): void {
    fs.ensureDirSync(this.logDir);
  }

  private setupLogger(): void {
    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    );

    const consoleFormat = winston.format.combine(
      winston.format.timestamp({ format: 'HH:mm:ss' }),
      winston.format.printf(({ level, message, timestamp, ...meta }) => {
        const coloredLevel = this.colorizeLevel(level);
        const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
        return `${chalk.gray(timestamp)} ${coloredLevel} ${message}${metaStr}`;
      })
    );

    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports: [
        new winston.transports.File({
          filename: path.join(this.logDir, 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        new winston.transports.File({
          filename: path.join(this.logDir, 'combined.log'),
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
      ],
    });

    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(
        new winston.transports.Console({
          format: consoleFormat,
        })
      );
    }
  }

  private colorizeLevel(level: string): string {
    switch (level) {
      case 'error':
        return chalk.red('ERROR');
      case 'warn':
        return chalk.yellow('WARN ');
      case 'info':
        return chalk.blue('INFO ');
      case 'debug':
        return chalk.green('DEBUG');
      default:
        return level.toUpperCase();
    }
  }

  public info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  public error(message: string, error?: Error | any): void {
    this.logger.error(message, error);
  }

  public debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  public verbose(message: string, meta?: any): void {
    this.logger.verbose(message, meta);
  }

  public task(taskId: string, message: string, meta?: any): void {
    this.info(`[Task:${taskId}] ${message}`, meta);
  }

  public agent(message: string, meta?: any): void {
    this.info(`[Agent] ${message}`, meta);
  }

  public tool(toolName: string, message: string, meta?: any): void {
    this.info(`[Tool:${toolName}] ${message}`, meta);
  }

  public session(sessionId: string, message: string, meta?: any): void {
    this.info(`[Session:${sessionId}] ${message}`, meta);
  }

  public performance(operation: string, duration: number, meta?: any): void {
    const color = duration > 5000 ? chalk.red : duration > 1000 ? chalk.yellow : chalk.green;
    this.info(`[Performance] ${operation} completed in ${color(`${duration}ms`)}`, meta);
  }

  public stream(message: string): void {
    if (process.env.NODE_ENV !== 'production') {
      process.stdout.write(message);
    }
  }

  public clearLine(): void {
    if (process.env.NODE_ENV !== 'production') {
      process.stdout.clearLine(0);
      process.stdout.cursorTo(0);
    }
  }

  public newLine(): void {
    if (process.env.NODE_ENV !== 'production') {
      process.stdout.write('\n');
    }
  }
}

export const logger = Logger.getInstance();
