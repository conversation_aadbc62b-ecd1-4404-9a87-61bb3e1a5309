"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const logger_1 = require("@/utils/logger");
const file_operations_1 = require("./file-operations");
const shell_commands_1 = require("./shell-commands");
class ToolRegistry {
    static instance;
    tools = new Map();
    categories = new Map();
    constructor() {
        this.registerBuiltinTools();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    registerBuiltinTools() {
        // Register file operations tools
        const fileOps = new file_operations_1.FileOperationsTool();
        this.registerTool(fileOps.getReadFileTool());
        this.registerTool(fileOps.getWriteFileTool());
        this.registerTool(fileOps.getCreateFileTool());
        this.registerTool(fileOps.getDeleteFileTool());
        this.registerTool(fileOps.getCopyFileTool());
        this.registerTool(fileOps.getMoveFileTool());
        this.registerTool(fileOps.getListDirectoryTool());
        this.registerTool(fileOps.getSearchFilesTool());
        this.registerTool(fileOps.getGrepTool());
        this.registerTool(fileOps.getGlobTool());
        this.registerTool(fileOps.getPermissionsTool());
        // Register shell commands tools
        const shellOps = new shell_commands_1.ShellCommandsTool();
        this.registerTool(shellOps.getExecuteCommandTool());
        this.registerTool(shellOps.getExecuteScriptTool());
        this.registerTool(shellOps.getProcessManagementTool());
        this.registerTool(shellOps.getEnvironmentTool());
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        // Add to category index
        if (!this.categories.has(tool.category)) {
            this.categories.set(tool.category, []);
        }
        this.categories.get(tool.category).push(tool.name);
        logger_1.logger.debug(`Registered tool: ${tool.name}`, { category: tool.category, riskLevel: tool.riskLevel });
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolsByCategory(category) {
        const toolNames = this.categories.get(category) || [];
        return toolNames.map(name => this.tools.get(name)).filter(Boolean);
    }
    getToolsByRiskLevel(riskLevel) {
        return this.getAllTools().filter(tool => tool.riskLevel === riskLevel);
    }
    getParallelTools() {
        return this.getAllTools().filter(tool => tool.parallel);
    }
    async executeTool(toolName, parameters, context) {
        const tool = this.getTool(toolName);
        if (!tool) {
            throw new Error(`Tool not found: ${toolName}`);
        }
        logger_1.logger.tool(toolName, 'Executing tool', { parameters, riskLevel: tool.riskLevel });
        const startTime = Date.now();
        try {
            // Validate parameters
            this.validateParameters(tool, parameters);
            // Execute tool
            const result = await tool.execute(parameters, context);
            const duration = Date.now() - startTime;
            logger_1.logger.performance(`Tool:${toolName}`, duration);
            logger_1.logger.tool(toolName, 'Tool execution completed', { success: result.success, duration });
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.logger.error(`Tool execution failed: ${toolName}`, error);
            return {
                success: false,
                data: null,
                error: error instanceof Error ? error.message : String(error),
                metadata: { duration, toolName }
            };
        }
    }
    async executeToolsParallel(executions, context, maxConcurrency = 5) {
        const pLimit = (await Promise.resolve().then(() => __importStar(require('p-limit')))).default;
        const limit = pLimit(maxConcurrency);
        const promises = executions.map(({ toolName, parameters }) => limit(() => this.executeTool(toolName, parameters, context)));
        return Promise.all(promises);
    }
    validateParameters(tool, parameters) {
        for (const param of tool.parameters) {
            if (param.required && !(param.name in parameters)) {
                throw new Error(`Missing required parameter: ${param.name}`);
            }
            if (param.name in parameters) {
                const value = parameters[param.name];
                // Type validation
                if (!this.validateParameterType(value, param.type)) {
                    throw new Error(`Invalid type for parameter ${param.name}: expected ${param.type}`);
                }
                // Enum validation
                if (param.enum && !param.enum.includes(value)) {
                    throw new Error(`Invalid value for parameter ${param.name}: must be one of ${param.enum.join(', ')}`);
                }
            }
            else if (param.default !== undefined) {
                parameters[param.name] = param.default;
            }
        }
    }
    validateParameterType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number';
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
    getToolsForLLM() {
        return this.getAllTools().map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters
        }));
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasRiskyTools(toolNames) {
        return toolNames.some(name => {
            const tool = this.getTool(name);
            return tool && (tool.riskLevel === 'high' || tool.riskLevel === 'critical');
        });
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=tool-registry.js.map