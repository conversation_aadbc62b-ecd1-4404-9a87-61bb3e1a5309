import { logger } from '@/utils/logger';
import { FileOperationsTool } from './file-operations';
import { ShellCommandsTool } from './shell-commands';
export class ToolRegistry {
    static instance;
    tools = new Map();
    categories = new Map();
    constructor() {
        this.registerBuiltinTools();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    registerBuiltinTools() {
        // Register file operations tools
        const fileOps = new FileOperationsTool();
        this.registerTool(fileOps.getReadFileTool());
        this.registerTool(fileOps.getWriteFileTool());
        this.registerTool(fileOps.getCreateFileTool());
        this.registerTool(fileOps.getDeleteFileTool());
        this.registerTool(fileOps.getCopyFileTool());
        this.registerTool(fileOps.getMoveFileTool());
        this.registerTool(fileOps.getListDirectoryTool());
        this.registerTool(fileOps.getSearchFilesTool());
        this.registerTool(fileOps.getGrepTool());
        this.registerTool(fileOps.getGlobTool());
        this.registerTool(fileOps.getPermissionsTool());
        // Register shell commands tools
        const shellOps = new ShellCommandsTool();
        this.registerTool(shellOps.getExecuteCommandTool());
        this.registerTool(shellOps.getExecuteScriptTool());
        this.registerTool(shellOps.getProcessManagementTool());
        this.registerTool(shellOps.getEnvironmentTool());
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        // Add to category index
        if (!this.categories.has(tool.category)) {
            this.categories.set(tool.category, []);
        }
        this.categories.get(tool.category).push(tool.name);
        logger.debug(`Registered tool: ${tool.name}`, { category: tool.category, riskLevel: tool.riskLevel });
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolsByCategory(category) {
        const toolNames = this.categories.get(category) || [];
        return toolNames.map(name => this.tools.get(name)).filter(Boolean);
    }
    getToolsByRiskLevel(riskLevel) {
        return this.getAllTools().filter(tool => tool.riskLevel === riskLevel);
    }
    getParallelTools() {
        return this.getAllTools().filter(tool => tool.parallel);
    }
    async executeTool(toolName, parameters, context) {
        const tool = this.getTool(toolName);
        if (!tool) {
            throw new Error(`Tool not found: ${toolName}`);
        }
        logger.tool(toolName, 'Executing tool', { parameters, riskLevel: tool.riskLevel });
        const startTime = Date.now();
        try {
            // Validate parameters
            this.validateParameters(tool, parameters);
            // Execute tool
            const result = await tool.execute(parameters, context);
            const duration = Date.now() - startTime;
            logger.performance(`Tool:${toolName}`, duration);
            logger.tool(toolName, 'Tool execution completed', { success: result.success, duration });
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.error(`Tool execution failed: ${toolName}`, error);
            return {
                success: false,
                data: null,
                error: error instanceof Error ? error.message : String(error),
                metadata: { duration, toolName }
            };
        }
    }
    async executeToolsParallel(executions, context, maxConcurrency = 5) {
        const { default: pLimit } = await import('p-limit');
        const limit = pLimit(maxConcurrency);
        const promises = executions.map(({ toolName, parameters }) => limit(() => this.executeTool(toolName, parameters, context)));
        return Promise.all(promises);
    }
    validateParameters(tool, parameters) {
        for (const param of tool.parameters) {
            if (param.required && !(param.name in parameters)) {
                throw new Error(`Missing required parameter: ${param.name}`);
            }
            if (param.name in parameters) {
                const value = parameters[param.name];
                // Type validation
                if (!this.validateParameterType(value, param.type)) {
                    throw new Error(`Invalid type for parameter ${param.name}: expected ${param.type}`);
                }
                // Enum validation
                if (param.enum && !param.enum.includes(value)) {
                    throw new Error(`Invalid value for parameter ${param.name}: must be one of ${param.enum.join(', ')}`);
                }
            }
            else if (param.default !== undefined) {
                parameters[param.name] = param.default;
            }
        }
    }
    validateParameterType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number';
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
    getToolsForLLM() {
        return this.getAllTools().map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters
        }));
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasRiskyTools(toolNames) {
        return toolNames.some(name => {
            const tool = this.getTool(name);
            return tool && (tool.riskLevel === 'high' || tool.riskLevel === 'critical');
        });
    }
}
//# sourceMappingURL=tool-registry.js.map