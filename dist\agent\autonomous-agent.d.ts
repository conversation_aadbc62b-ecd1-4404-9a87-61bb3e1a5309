import { AgentConfig, AgentContext, ExecutionPlan, TaskResult, LLMMessage, ToolCall } from '@/types';
export declare class AutonomousAgent {
    private provider;
    private toolRegistry;
    private planner;
    private executor;
    private contextManager;
    private sessionManager;
    private config;
    private conversationHistory;
    constructor(config: AgentConfig);
    private initializeSystemPrompt;
    processRequest(userInput: string): Promise<{
        plan: ExecutionPlan;
        results: TaskResult[];
        summary: string;
    }>;
    private generatePlanningResponse;
    private buildContextMessage;
    private generateSummary;
    private updateConversationWithResults;
    private learnFromExecution;
    handleToolCall(toolCall: ToolCall, context: AgentContext): Promise<TaskResult>;
    getConversationHistory(): LLMMessage[];
    clearConversationHistory(): void;
    streamResponse(userInput: string, onChunk: (chunk: string) => void): Promise<void>;
}
//# sourceMappingURL=autonomous-agent.d.ts.map