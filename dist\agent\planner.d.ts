import { Execution<PERSON><PERSON>, Tool<PERSON>all, AgentContext } from '@/types';
import { ToolRegistry } from '@/tools/tool-registry';
export declare class TaskPlanner {
    private toolRegistry;
    constructor(toolRegistry: ToolRegistry);
    createExecutionPlan(toolCalls: ToolCall[], context: AgentContext): Promise<ExecutionPlan>;
    private convertToolCallsToTasks;
    private mapToolCategoryToTaskType;
    private calculateTaskPriority;
    private getMaxRetries;
    private getTaskTimeout;
    private analyzeDependencies;
    private hasDependency;
    private identifyParallelGroups;
    private calculateDependencyLevels;
    private calculateMaxConcurrency;
    private estimateExecutionTime;
    optimizePlan(plan: ExecutionPlan): ExecutionPlan;
    private getTaskLevel;
}
//# sourceMappingURL=planner.d.ts.map